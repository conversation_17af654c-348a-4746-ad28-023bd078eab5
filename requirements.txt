# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==4.8.0
    # via starlette
click==8.1.8
    # via uvicorn
dnspython==2.7.0
    # via pymongo
fastapi==0.115.11
    # via backend-template (pyproject.toml)
h11==0.14.0
    # via uvicorn
idna==3.10
    # via anyio
pydantic==2.10.6
    # via fastapi
pydantic-core==2.27.2
    # via pydantic
pymongo==4.11.2
    # via backend-template (pyproject.toml)
python-dotenv==1.0.1
    # via backend-template (pyproject.toml)
sniffio==1.3.1
    # via anyio
starlette==0.46.1
    # via fastapi
typing-extensions==4.12.2
    # via
    #   anyio
    #   fastapi
    #   pydantic
    #   pydantic-core
uvicorn==0.34.0
    # via backend-template (pyproject.toml)
