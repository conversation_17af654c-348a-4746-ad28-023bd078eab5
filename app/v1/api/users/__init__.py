from fastapi import APIRouter, HTTPException, Depends, Request, Form
from fastapi.responses import RedirectResponse, HTMLResponse
from bson import ObjectId
import secrets
from datetime import timedelta

from app.core.config import SECRET_KEY, ALGORITHM
from app.core.logger import StructuredLogger
from app.core.security import  get_tenant_info, require_roles
from app.core.database import get_db_from_tenant_id, get_tenant_id_and_name_from_slug
from app.core.auth_helpers import AuthHelper
from app.models.security import OAuth2PasswordRequestFormWithClientID, ChangePasswordRequest, ResetPasswordRequest
from app.core.security import (
    create_access_token,
    verify_password,
    create_invitation_token,
    verify_invitation_token,
    get_tenant_info,
    require_roles,
    ph  # Replace pwd_context with ph
)
from app.models.user import AgentInvitation, AgentRegistration
from app.core.helper.mongo_helper import convert_objectid_to_str
from datetime import timedelta
from app.models.current_user import CurrentUser
from datetime import datetime, timedelta
from app.v1.api.users.user_permissions import router as permissions_router
logger = StructuredLogger(__name__)

router = APIRouter(tags=["Users"])

# Include the permissions router
router.include_router(permissions_router)


@router.get("/get_tenant_id")
async def tenantid_from_slug(slug: str):
    """Get tenant ID and name from slug using async operations"""
    try:
        tenant_info = await AuthHelper.get_tenant_info_by_slug(slug)
        return {
            "tenant_id": tenant_info["tenant_id"],
            "tenant_name": tenant_info["tenant_name"],
            "status": tenant_info["status"]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting tenant info: {e}")
        raise HTTPException(status_code=500, detail="Failed to get tenant information")

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends()):
    """Async login with role-based database access"""
    try:
        # Get tenant info
        tenant_info = await AuthHelper.get_tenant_info_by_slug(form_data.client_id)

        # Authenticate user
        user_tenant_db = await AuthHelper.authenticate_user(
            username=form_data.username,
            password=form_data.password,
            tenant_slug=form_data.client_id
        )

        if not user_tenant_db:
            logger.issue(f"Authentication failed for user: {form_data.username}")
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Create access token with role
        access_token = create_access_token(
            data={
                "sub": user_tenant_db.user.username,
                "role": user_tenant_db.user.role,
                "tenant_id": user_tenant_db.tenant_id
            },
            expires_delta=timedelta(days=1)
        )

        logger.log(
            f"User {user_tenant_db.user.username} logged in to tenant {user_tenant_db.tenant_name} "
            f"with role: {user_tenant_db.user.role}"
        )

        return {
            "id": user_tenant_db.user.id,
            "access_token": access_token,
            "token_type": "bearer",
            "username": user_tenant_db.user.username,
            "role": user_tenant_db.user.role,
            "has_write_access": user_tenant_db.has_write_access(),
            "tenant_id": user_tenant_db.tenant_id,
            "tenant_label": user_tenant_db.tenant_name,
            "tenant_slug": form_data.client_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")





@router.get("/oauth/login")
async def oauth_login_page(request: Request, redirect_uri: str = None):
    """OAuth login page with form"""
    login_form_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Login - Legal Backend</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
            .login-container {{ max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .form-group {{ margin-bottom: 15px; }}
            label {{ display: block; margin-bottom: 5px; font-weight: bold; }}
            input[type="text"], input[type="password"] {{ width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }}
            button {{ width: 100%; padding: 12px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }}
            button:hover {{ background-color: #0056b3; }}
            .error {{ color: red; margin-top: 10px; }}
            .info {{ color: #666; font-size: 14px; margin-bottom: 20px; }}
        </style>
    </head>
    <body>
        <div class="login-container">
            <h2>Legal Backend Login</h2>
            <div class="info">
                <strong>Default Credentials:</strong><br>
                Username: admin<br>
                Password: admin123<br>
                Client ID: legal_backend
            </div>
            <form method="post" action="/v1/oauth/callback">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" value="admin" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" value="admin123" required>
                </div>
                <div class="form-group">
                    <label for="client_id">Client ID:</label>
                    <input type="text" id="client_id" name="client_id" value="legal_backend" required>
                </div>
                <input type="hidden" name="redirect_uri" value="{redirect_uri or '/v1/docs'}">
                <button type="submit">Login</button>
            </form>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=login_form_html)


@router.post("/oauth/callback")
async def oauth_callback(
    username: str = Form(...),
    password: str = Form(...),
    client_id: str = Form(...),
    redirect_uri: str = Form("/v1/docs")
):
    """OAuth callback that handles form submission and redirects with token"""
    try:
        # Authenticate user
        user_tenant_db = await AuthHelper.authenticate_user(
            username=username,
            password=password,
            tenant_slug=client_id
        )

        if not user_tenant_db:
            # Return to login page with error
            error_html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Login Error</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                    .error-container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    .error { color: red; margin-bottom: 20px; }
                    a { color: #007bff; text-decoration: none; }
                    a:hover { text-decoration: underline; }
                </style>
            </head>
            <body>
                <div class="error-container">
                    <h2>Login Failed</h2>
                    <div class="error">Invalid credentials. Please check your username, password, and client ID.</div>
                    <a href="/v1/oauth/login">← Back to Login</a>
                </div>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=401)

        # Create access token
        access_token = create_access_token(
            data={
                "sub": user_tenant_db.user.username,
                "role": user_tenant_db.user.role,
                "tenant_id": user_tenant_db.tenant_id
            },
            expires_delta=timedelta(days=1)
        )

        # Redirect to the specified URI with token as fragment (for frontend apps)
        # or as query parameter (for server-side apps)
        if "docs" in redirect_uri or "swagger" in redirect_uri:
            # For API docs, redirect directly
            return RedirectResponse(url=redirect_uri)
        else:
            # For other apps, include token in URL fragment
            redirect_url = f"{redirect_uri}#access_token={access_token}&token_type=bearer"
            return RedirectResponse(url=redirect_url)

    except Exception as e:
        logger.exception(f"OAuth callback error: {e}")
        error_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Login Error</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                .error-container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .error { color: red; margin-bottom: 20px; }
                a { color: #007bff; text-decoration: none; }
                a:hover { text-decoration: underline; }
            </style>
        </head>
        <body>
            <div class="error-container">
                <h2>Login Error</h2>
                <div class="error">An error occurred during login. Please try again.</div>
                <a href="/v1/oauth/login">← Back to Login</a>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=error_html, status_code=500)


@router.get("/verify_token")
async def verify_token(current_user: CurrentUser = Depends(get_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_current_user will raise an exception
    """
    return True


@router.get("/test_clients")
async def test_clients(current_user: CurrentUser = Depends(get_tenant_info)):
    """Test Qdrant and LLM clients"""
    result = {
        "user": current_user.user.username,
        "tenant": current_user.tenant_name,
        "qdrant_available": current_user.qdrant is not None,
        "qdrant_index_available": current_user.qd_index is not None,
        "llm_available": current_user.llm is not None,
        "openai_available": False,
        "gemini_available": False
    }

    if current_user.llm:
        result["openai_available"] = current_user.llm.openai is not None
        result["gemini_available"] = current_user.llm.gemini is not None

    return result


@router.post("/users/invite")
async def invite_agent(
    invitation: AgentInvitation,
    current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))
    ):
    """
    Invite a new agent by generating a registration link with a token.
    Only admins and supervisors can invite agents.
    """

    # invited_by = "god"
    tenant_id = current_user.tenant_id
    users_collection = current_user.db.users
    tenant_roles = current_user.user.role
    # if tenant_role not in ["admin", "supervisor"]:
    #     raise HTTPException(status_code=403, detail="Not authorized to invite agents")

    invited_by = str(tenant_id)
    print(invited_by)

    existing_user = await users_collection.find_one({"username": invitation.username})
    if existing_user:
        return {"registration_token": None, "success": False, "msg": "Username already exists"}
    
    invitations_collection = current_user.db.invitations
    existing_invitation = await invitations_collection.find_one({"username": invitation.username})
    if existing_invitation:
        try:
            result = await invitations_collection.delete_many({"username": invitation.username})
            if result.deleted_count > 0:
                logger.log(f"Record with username '{invitation.username}' existed, therefore deleted existing invitation to create new one.")
        except HTTPException as e:
            logger.exception(f"Error deleting existing invitation: {e}")
            raise e

    # # Create invitation token
    token = create_invitation_token(username=invitation.username,
                                    role=invitation.role,
                                    invited_by=invited_by,
                                    tenant_id=tenant_id,
                                    expires_delta=timedelta(days=7))  # Valid for 7 days

    invitation_record = {
        "username": invitation.username,
        "token": token,
        "role": invitation.role,
        "invited_by": invited_by,
        "expires_at": datetime.now() + timedelta(days=7),
        "used": False,
        # "permissions":invitation.permissions
    }

    await invitations_collection.insert_one(invitation_record)
    
    return {"registration_token": token, "success": True, "msg": "Token Generated!"}


@router.post("/users/register")
async def register_agent(registration: AgentRegistration):
    """
    Register a new agent using the invitation token.
    """
    # Verify the token and extract the agent's name
    try:
        agent_username, invited_by, roles_ = verify_invitation_token(registration.token)
    except HTTPException as e:
        logger.exception(f"Error verifying invitation token: {e}")
        raise e
    
    tenant_database =  get_db_from_tenant_id(invited_by)
    invitations_collection = tenant_database.invitations
    invitation = await invitations_collection.find_one({"token": registration.token})

    if not invitation:
        return {"msg": "Invalid invitation token!", "success": False}
    if invitation.get("used"):
        return {"msg": "Invitation token has already been used. Request the supervisor to generate a new one!", "success": False}
    
    users_collection = tenant_database.users
    # Check if the desired username already exists
    existing_user = await users_collection.find_one({"username": agent_username})
    if existing_user:
        return {"msg": "Username already exists!", "success": False}
    
    # Hash the provided password
    hashed_password = ph.hash(registration.password)

    role_doc = await tenant_database.roles.find_one({"name": role_})
    permissions = role_doc.get("default_permissions") if role_doc else []
    
    # Create the new agent user
    new_agent = {
        "username": registration.username,
        "hashed_password": hashed_password,
        "role": role_,
        "created_by": invited_by,
        "created_at": datetime.now(),
        "permissions":permissions
    }
    
    # Insert the new agent into the database
    result = await users_collection.insert_one(new_agent)
    new_agent["_id"] = result.inserted_id

    await invitations_collection.update_one(
        {"token": registration.token},
        {"$set": {"used": True}}
    )
    
    return {"msg": "Agent registered successfully", "success": True}



@router.post("/users/change_password")
async def change_password(req: ChangePasswordRequest,
                          current_user: CurrentUser = Depends(get_tenant_info)):
    
    users_collection = current_user.db.read_db.users
    
    user = await users_collection.find_one({"_id": ObjectId(current_user.user.id)}) 
    if not user:
        raise HTTPException(
            status_code=404, 
            detail="User not found."
        )
    
    if not verify_password(req.old_password, user["hashed_password"]):
        raise HTTPException(
            status_code=400,
            detail="Current password is incorrect."
        )
    
    if verify_password(req.new_password, user["hashed_password"]):
        raise HTTPException(
            status_code=400,
            detail="New password must be different from current password"
        )
    
    new_hashed_password = ph.hash(req.new_password)
    result = await users_collection.update_one(
        {"_id": ObjectId(current_user.user.id)},
        {"$set": {"hashed_password": new_hashed_password}}
    )
    
    if result.modified_count == 0:
        raise HTTPException(
            status_code=500,
            detail="Failed to update password."
        )
    
    logger.log(f"Password successfully changed for user: {user['username']}")
    return {"message": "Password successfully changed."}

@router.post("/users/reset_password")
async def reset_password(
    req: ResetPasswordRequest,
    current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))
):
    users_collection = current_user.db.read_db.users
    
    user = await users_collection.find_one({"username": current_user.user.username})
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    
    new_random_password = ''.join(secrets.choice('0123456789abcdef') for _ in range(10))
    new_hashed_password = ph.hash(new_random_password)
    
    if req.subordinate_id:
        subordinate = await users_collection.find_one({"_id": ObjectId(req.subordinate_id)})
        if not subordinate:
            raise HTTPException(status_code=404, 
                                detail="Subordinate user doesn't exist.")
        
        result = await users_collection.update_one(
            {"_id": ObjectId(req.subordinate_id)},
            {"$set": {"hashed_password": new_hashed_password}}
        )
        
        logger.log(f"Subordinate Password reset successfully by user: {user['username']}")
        return {"message": f"Subordinate Password reset to {new_random_password} successfully."}
    
    result = await users_collection.update_one(
        {"_id": ObjectId(user["_id"])},
        {"$set": {"hashed_password": new_hashed_password}}
    )
    
    if result.modified_count == 0:
        raise HTTPException(
            status_code=500,
            detail="Failed to reset password."
        )
    
    logger.log(f"Password reset successfully by user: {user['username']}")
    return {"message": f"Password reset to {new_random_password} successfully."}


# Remove the reset_permissions endpoint from here