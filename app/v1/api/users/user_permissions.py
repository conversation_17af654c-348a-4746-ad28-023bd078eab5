from fastapi import APIRouter, HTTPException, Depends
from bson import ObjectId

from app.core.logger import StructuredLogger
from app.core.security import get_tenant_info, require_roles
from app.models.current_user import CurrentUser

logger = StructuredLogger(__name__)

router = APIRouter(tags=["User Permissions"])

@router.get("/users/{user_id}/permissions")
async def get_all_permissions(
    user_id: str,
    current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))
):
    """Get all of the user's permissions"""
    users_collection = current_user.db.read_db.users
    
    if user_id == "me":
        user = await users_collection.find_one({"_id": ObjectId(current_user.user.id)})
    else:
        try:
            user = await users_collection.find_one({"_id": ObjectId(user_id)})
        except:
            raise HTTPException(status_code=400, detail="Invalid user ID format")
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return {"permissions": user.get("permissions", [])}

@router.post("/users/permissions/reset")
async def reset_permissions(
    user_id: str,
    current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))
):
    """
    Reset a user's permissions to their role's default permissions.
    Only admins can reset permissions.
    """
    users_collection = current_user.db.read_db.users
    roles_collection = current_user.db.read_db.roles

    # Handle "me" case or specific user_id
    try:
        user = await users_collection.find_one({"_id": ObjectId(user_id)})
    except:
        raise HTTPException(status_code=400, detail="Invalid user ID format")

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Get the default permissions for the user's role
    role_info = await roles_collection.find_one({"name": user["role"]})
    if not role_info:
        raise HTTPException(
            status_code=404,
            detail=f"User's role: '{user['role']}' not found in database"
        )

    default_permissions = role_info.get("default_permissions")
    if default_permissions is None:
        raise HTTPException(
            status_code=404,
            detail=f"No default permissions found for role '{user['role']}'"
        )

    # Update the user's permissions
    result = await users_collection.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": {"permissions": default_permissions}}
    )

    if result.modified_count == 0:
        raise HTTPException(
            status_code=500,
            detail="Failed to update permissions"
        )

    logger.log(f"Permissions reset to default for user: {user['username']}")
    return {
        "message": "Permissions reset successfully",
        "username": user["username"],
        "role": user["role"]
    }
