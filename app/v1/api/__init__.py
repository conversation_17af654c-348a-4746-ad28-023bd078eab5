from fastapi import FastAPI, responses

from .users import router as user_router
from .config import router as config_router
from .roles import router as role_router
from .permissions import router as permissions_router
from .setup import router as setup_router
from .knowledgebase import router as knowledgebase_router
from .chat.routes import router as chat_router


v1_api = FastAPI(
    title="Legal Backend - API V1",
    version="1.0",
    description="Version 1 of the Legal Backend API with OAuth2 authentication",
    docs_url="/docs",
    redoc_url="/redoc",
    swagger_ui_oauth2_redirect_url="/v1/docs/oauth2-redirect",
    openapi_url="/openapi.json",
    servers=[
        {"url": "/v1", "description": "API V1"}
    ]
)

@v1_api.get("/hello")
async def hello_v1():
    return {"message": "Hello from V1"}


@v1_api.get("/", include_in_schema=False)
async def v1_root():
    return responses.RedirectResponse(url="/v1/docs")


@v1_api.get("/docs/oauth2-redirect", include_in_schema=False)
async def oauth2_redirect():
    """OAuth2 redirect endpoint for Swagger UI"""
    return responses.HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>OAuth2 Redirect</title>
    </head>
    <body>
        <script>
            // This handles the OAuth2 redirect for Swagger UI
            if (window.opener) {
                window.opener.swaggerUIRedirectOauth2 && window.opener.swaggerUIRedirectOauth2(window.location);
                window.close();
            }
        </script>
    </body>
    </html>
    """)

v1_api.include_router(user_router)
v1_api.include_router(config_router)
v1_api.include_router(role_router)
v1_api.include_router(permissions_router)
v1_api.include_router(setup_router)
v1_api.include_router(knowledgebase_router)
v1_api.include_router(chat_router)
