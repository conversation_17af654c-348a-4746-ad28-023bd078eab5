from fastapi import APIRouter
from app.v1.api.chat.routes.retrieve import router as query_router
from app.core.logger import StructuredLogger
# from app.v1.api.chat.routes.lang_agents import router as lang_agent_router
# Create a new router for chat-related endpoints
# from app.v1.api.chat.routes.query import router as query_engine_router
from app.v1.api.chat.routes.legal_rag import router as legal_rag_router
# from app.v1.api.chat.core.agent import router as agent_router
from app.v1.api.chat.core.simple_agent import router as agent_router
# from app.v1.api.chat.routes.lang_ import router as lang_router
logger = StructuredLogger(__name__)
router = APIRouter(prefix="/chat", tags=["Chat"])

router.include_router(query_router)

# router.include_router(query_engine_router)
router.include_router(legal_rag_router)
# router.include_router(lang_agent_router)
# router.include_router(lang_router)
router.include_router(agent_router)
