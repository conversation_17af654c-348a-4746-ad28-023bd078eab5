# from typing import Dict, Any
# import asyncio
# from fastapi import Depends, API<PERSON><PERSON>er, HTTPException
# from langchain.agents import initialize_agent, AgentType
# from langchain.memory.buffer import ConversationBufferMemory
# from langchain.tools import Tool
# from langchain_openai import OpenAI
# from llama_index.core.query_engine import Citation<PERSON>ueryEngine, RetrySourceQueryEngine, RetrieverQueryEngine
# from llama_index.core.retrievers import VectorIndexRetriever
# from llama_index.core.evaluation import RelevancyEvaluator
# from app.core.security import get_tenant_info
# from app.models.current_user import CurrentUser
# from app.core.logger import StructuredLogger

# router = APIRouter()
# logger = StructuredLogger(__name__)

# @router.post("/lang_agent")
# async def lang_agent(query: str, current_user: CurrentUser = Depends(get_tenant_info)):
#     """
#     Initialize and run a LangChain agent with multiple parallel query engines.
#     Combines results from different retrieval strategies for comprehensive responses.
#     """
#     try:
#         # Hardcoded engine configurations
#         index = current_user.qd_index_sync
#         llm = current_user.llm.openai
        
#         # Create all engines directly
#         base_retriever = VectorIndexRetriever(index=index, similarity_top_k=5)
        
#         engines = [
#             # Basic retriever with small context
#             RetrieverQueryEngine.from_args(
#                 retriever=base_retriever,
#                 llm=llm,
#                 similarity_top_k=3
#             ),
            
#             # Citation engine for legal references
#             CitationQueryEngine.from_args(
#                 index=index,
#                 similarity_top_k=5,
#                 citation_chunk_size=512,
#                 llm=llm,
#             ),
            
#             # Retry engine for better context understanding
#             RetrySourceQueryEngine(
#                 query_engine=RetrieverQueryEngine.from_args(
#                     retriever=base_retriever,
#                     llm=llm
#                 ),
#                 evaluator=RelevancyEvaluator(),
#                 llm=llm
#             ),
            
#             # Dense retriever with more context
#             RetrieverQueryEngine.from_args(
#                 retriever=VectorIndexRetriever(index=index, similarity_top_k=8),
#                 llm=llm,
#                 similarity_top_k=5
#             )
#         ]
        
#         # Create tools with unique names and descriptions
#         tools = [
#             # Basic retriever tool
#             Tool(
#                 name="basic_legal_retriever",
#                 func=lambda q, e=engines[0]: (r := e.query(q)) and r.response + "\n\n" + "\n\n".join(s.node.text for s in r.source_nodes),
#                 description="Basic legal document retriever with small context window. Best for simple queries."
#             ),
            
#             # Citation engine tool
#             Tool(
#                 name="legal_citation_finder",
#                 func=lambda q, e=engines[1]: (r := e.query(q)) and r.response + "\n\n" + "\n\n".join(s.node.text for s in r.source_nodes),
#                 description="Finds and cites legal references. Best for queries requiring specific legal citations."
#             ),
            
#             # Retry engine tool
#             Tool(
#                 name="context_aware_legal_search",
#                 func=lambda q, e=engines[2]: (r := e.query(q)) and r.response + "\n\n" + "\n\n".join(s.node.text for s in r.source_nodes),
#                 description="Advanced search with context understanding. Best for complex legal questions."
#             ),
            
#             # Dense retriever tool
#             Tool(
#                 name="comprehensive_legal_search",
#                 func=lambda q, e=engines[3]: (r := e.query(q)) and r.response + "\n\n" + "\n\n".join(s.node.text for s in r.source_nodes),
#                 description="Comprehensive legal document search with expanded context. Best for thorough research."
#             )
#         ]
        
#         # Initialize agent
#         agent = initialize_agent(
#             tools=tools,
#             agent_type=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
#             memory=ConversationBufferMemory(memory_key="chat_history", return_messages=True),
#             llm=OpenAI(api_key=current_user.llm.openai.api_key, model_name="gpt-4o-mini"),
#             verbose=True,
#             return_intermediate_steps=True
#         )
        
#         # Execute agent and get response
#         agent_response = agent.invoke({"input": query})
        
       
        
#         return {
#             "response": agent_response,
#         }
        
#     except Exception as e:
#         logger.error(f"Error in lang_agent: {str(e)}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"Error processing your request: {str(e)}"
#         )
    
                        
            