"""
Qdrant client for vector database operations
"""

from app.core.logger import StructuredLogger
from typing import Optional
from qdrant_client import AsyncQdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import VectorStoreIndex
from llama_index.core import StorageContext

logger = StructuredLogger(__name__)


from qdrant_client import QdrantClient

async def create_qdrant_clients(config_db):
    """Create both async and sync Qdrant clients from config"""
    try:
        # Get Qdrant config from database
        qdrant_config = await config_db.config.find_one({"name": "qdrant_config"})

        if not qdrant_config:
            logger.warning("Qdrant config not found in database")
            return None, None, None, None

        host = qdrant_config["host"]
        port = qdrant_config["port"]
        sent_coll_name = qdrant_config.get("search_coll", "legal_sentence_split")

        # Create async client and index
        async_client = AsyncQdrantClient(host=host, port=port)
        logger.info(f"Qdrant async client created for {host}:{port}")
        
        # Create sync client and index
        sync_client = QdrantClient(host=host, port=port)
        logger.info(f"Qdrant sync client created for {host}:{port}")
        
        # Create async vector store and index
        async_vector_store = user_tenant_db.user.id(
            aclient=async_client, 
            client=sync_client,
            collection_name=sent_coll_name
        )
        async_storage = StorageContext.from_defaults(vector_store=async_vector_store)
        async_index = VectorStoreIndex.from_vector_store(
            vector_store=async_vector_store,
            storage_context=async_storage
        )
        
        # Create sync vector store and index
        sync_vector_store = user_tenant_db.user.id(
            aclient=async_client,
            client=sync_client,
            collection_name=sent_coll_name
        )
        sync_storage = StorageContext.from_defaults(vector_store=sync_vector_store)
        sync_index = VectorStoreIndex.from_vector_store(
            vector_store=sync_vector_store,
            storage_context=sync_storage
        )
        
        return async_client, sync_client, async_index, sync_index

    except Exception as e:
        logger.error(f"Failed to create Qdrant client: {e}")
        return None
 