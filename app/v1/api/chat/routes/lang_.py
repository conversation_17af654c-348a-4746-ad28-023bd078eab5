# from typing import Dict, Any, List, Optional, Annotated
# import asyncio
# from pydantic import BaseModel, Field
# from fastapi import APIRouter, Depends
# import json
# import re

# # LangGraph and LangChain imports (2025 version)
# from langgraph.graph import StateGraph, END, START
# from langgraph.graph.message import MessagesState
# from langgraph.prebuilt import ToolNode
# from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, ToolMessage
# from langchain_core.tools import tool
# from langchain_openai import ChatOpenAI
# from langchain_core.prompts import ChatPromptTemplate

# # LlamaIndex imports for retrieval
# from llama_index.core.query_engine import CitationQueryEngine
# from llama_index.core.prompts import PromptTemplate
# from llama_index.core.retrievers import BaseRetriever

# from app.core.security import get_tenant_info
# from app.models.user import UserTenantDB
# from app.core.logger import StructuredLogger
# from app.v1.api.chat.routes.legal_rag import _merge_sources
# from collections import defaultdict
# from bson import ObjectId

# logger = StructuredLogger(__name__)
# router = APIRouter()

# class CitationSource(BaseModel):
#     title: str = Field(description="Source title")
#     summary: str = Field(description="Source summary") 
#     citation_id: int = Field(description="Citation ID number")
#     text: str = Field(description="Source text excerpt")
#     metadata: Dict[str, Any] = Field(default_factory=dict, description="Source metadata")
#     page_number: Optional[int] = Field(default=None, description="Page number if available")
#     document_name: Optional[str] = Field(default=None, description="Document name")

# class LegalResponse(BaseModel):
#     response: str = Field(description="Legal response with [1], [2] citations")
#     citations: List[CitationSource] = Field(description="Citation sources")
#     query: str = Field(description="Original query")
#     status: str = Field(description="Response status")
#     source_count: int = Field(description="Number of sources found")

# # Enhanced citation template for better structured output
# CITATION_TEMPLATE = PromptTemplate(
#     """Based on the provided legal sources, provide a comprehensive answer with proper citations.

# Sources:
# {context_str}

# Query: {query_str}

# Instructions:
# 1. Provide a brief summary first in Nepali
# 2. Then detail each relevant case with proper structure
# 3. Use citations [1], [2], etc. for each source reference
# 4. DO NOT create fake URLs or source links
# 5. Only use the actual source information provided

# Answer in this format:
# ## संक्षिप्त विवरण
# [Brief summary here]

# ## केसहरूको विस्तृत विवरण
# ### केस १: [Case name from source]
# [Case details with citations]

# ### केस २: [Case name from source] 
# [Case details with citations]

# Answer:"""
# )

# class LegalAgentState(MessagesState):
#     """State for the legal research agent"""
#     citations: List[CitationSource] = Field(default_factory=list)
#     query: str = ""
#     final_response: str = ""
#     source_count: int = 0

# async def _process_sources_async(result, current_user: UserTenantDB) -> Dict[str, Any]:
#     """Async version of _process_sources that properly awaits MongoDB calls"""
#     try:
#         source_nodes = result.source_nodes
#         response_text = result.response if hasattr(result, 'response') else str(result)
        
#         # Create mapping between citation numbers and source nodes
#         citation_mapping = {}
        
#         # Group nodes by article_mongo_id like in /retrieve route
#         articles_dict = defaultdict(list)
        
#         for i, node in enumerate(source_nodes):
#             metadata = node.metadata if hasattr(node, 'metadata') else {}
#             article_id = metadata.get('article_mongo_id', 'unknown')
#             chunk_id = metadata.get('chunk_id', 'unknown')
            
#             # Get actual chunk text from split_articles collection (async version)
#             try:
#                 chunk_doc = await current_user.read_db.split_articles.find_one(
#                     {"metadata.article_mongo_id": article_id, "metadata.chunk_id": chunk_id},
#                     {"text": 1, "_id": 0}
#                 )
#                 chunk_text = chunk_doc.get('text', node.text) if chunk_doc else node.text
#             except Exception as e:
#                 logger.error(f"Error fetching chunk text: {e}")
#                 chunk_text = node.text
            
#             # Use citation number from CitationQueryEngine (1-based indexing)
#             citation_number = i + 1
#             chunk_citation_id = f"[{citation_number}]"
            
#             # Map citation number to source info
#             citation_mapping[citation_number] = {
#                 "article_id": article_id,
#                 "chunk_id": chunk_id,
#                 "text": chunk_text,
#                 "metadata": metadata
#             }
            
#             chunk_data = {
#                 "chunk_id": node.node_id if hasattr(node, 'node_id') else str(hash(node.text)),
#                 "citation_id": chunk_citation_id,
#                 "citation_number": citation_number,
#                 "score": round(float(node.score or 0), 4) if hasattr(node, 'score') else 0.0,
#                 "text": chunk_text,
#                 "metadata": metadata or {}
#             }
            
#             articles_dict[article_id].append(chunk_data)
        
#         # Sort chunks within each article by score (highest first)
#         for article_id in articles_dict:
#             articles_dict[article_id].sort(key=lambda x: x['score'], reverse=True)
        
#         # Convert to list format and fetch complete article data
#         sources = []
#         for article_id, chunks in articles_dict.items():
#             # Skip unknown articles
#             if article_id == 'unknown':
#                 continue
            
#             # Fetch complete article from documents collection (async version)
#             try:
#                 article_doc = await current_user.read_db.documents.find_one({"_id": ObjectId(article_id)})
#                 if not article_doc:
#                     logger.error(f"Article with ID {article_id} not found in database")
#                     continue
                
#                 # Convert ObjectId to string for JSON serialization
#                 article_metadata = {}
#                 for key, value in article_doc.items():
#                     if key == '_id':
#                         article_metadata['_id'] = str(value)
#                     else:
#                         article_metadata[key] = value
                
#                 # Collect all citation numbers from chunks in this article
#                 citation_numbers = [chunk['citation_number'] for chunk in chunks]
#                 citation_ids = [chunk['citation_id'] for chunk in chunks]
                
#                 article_data = {
#                     "article_mongo_id": article_id,
#                     "citation_numbers": citation_numbers,
#                     "citation_ids": citation_ids,
#                     "metadata": article_metadata,
#                     "chunks": chunks
#                 }
                
#                 sources.append(article_data)
                
#             except Exception as e:
#                 logger.error(f"Error fetching article {article_id}: {str(e)}")
#                 continue
        
#         # Sort articles by best chunk score
#         sources.sort(key=lambda x: max(chunk['score'] for chunk in x['chunks']), reverse=True)
        
#         return {
#             "response": response_text,
#             "sources": sources,
#             "citation_mapping": citation_mapping
#         }
        
#     except Exception as e:
#         logger.error(f"Error in async _process_sources: {e}")
#         return {
#             "response": str(result),
#             "sources": [],
#             "citation_mapping": {}
#         }

# def extract_citations_from_processed_sources(processed_data: Dict[str, Any]) -> List[CitationSource]:
#     """Extract citations from processed sources using proper _process_sources output"""
#     citations = []
    
#     try:
#         sources = processed_data.get('sources', [])
#         citation_mapping = processed_data.get('citation_mapping', {})
        
#         # Process each article and its chunks
#         for article in sources:
#             article_metadata = article.get('metadata', {})
#             chunks = article.get('chunks', [])
            
#             # Get article title from metadata
#             title = article_metadata.get('title', article_metadata.get('file_name', 'कानूनी दस्तावेज'))
#             doc_name = article_metadata.get('file_name', 'Unknown Document')
            
#             # Process each chunk in the article
#             for chunk in chunks:
#                 citation_number = chunk.get('citation_number', 0)
#                 chunk_text = chunk.get('text', '')
#                 chunk_metadata = chunk.get('metadata', {})
                
#                 # Create summary from chunk text
#                 summary = chunk_text[:300] + "..." if len(chunk_text) > 300 else chunk_text
                
#                 # Get page number from chunk metadata
#                 page_num = chunk_metadata.get('page_number', chunk_metadata.get('page'))
                
#                 citation = CitationSource(
#                     citation_id=citation_number,
#                     title=title,
#                     summary=summary,
#                     text=chunk_text,
#                     metadata=chunk_metadata,
#                     page_number=page_num,
#                     document_name=doc_name
#                 )
#                 citations.append(citation)
        
#         # Sort citations by citation_id
#         citations.sort(key=lambda x: x.citation_id)
        
#         logger.info(f"Successfully extracted {len(citations)} citations from {len(sources)} articles")
        
#     except Exception as e:
#         logger.error(f"Error extracting citations from processed sources: {e}")
    
#     return citations

# def create_legal_research_tool(current_user: UserTenantDB):
#     """Create LangChain tool that uses LlamaIndex retriever"""
    
#     @tool
#     async def legal_research_tool(query: str) -> str:
#         """Search legal documents using LlamaIndex and return structured results with citations.
        
#         Args:
#             query: The legal research query in Nepali
            
#         Returns:
#             JSON string with research results and citations
#         """
#         try:
#             logger.info(f"Executing legal research for query: {query}")
            
#             # Create citation engine using existing LlamaIndex setup
#             citation_engine = CitationQueryEngine.from_args(
#                 index=current_user.qd_index_sync,
#                 citation_chunk_size=1024,
#                 similarity_top_k=8,  # Get more sources
#                 llm=current_user.llm.openai,
#                 text_qa_template=CITATION_TEMPLATE,
#             )
            
#             # Perform query
#             result = citation_engine.query(query)
#             logger.info(f"Query completed, result type: {type(result)}")
            
#             # Process sources using the async version
#             processed_data = await _process_sources_async(result, current_user)
            
#             logger.info(f"Processed sources: {len(processed_data.get('sources', []))} articles")
            
#             # Extract citations from processed sources
#             citations = extract_citations_from_processed_sources(processed_data)
#             logger.info(f"Extracted {len(citations)} citations")
            
#             # Create structured result with full processed data
#             research_result = {
#                 "response_text": processed_data.get('response', ''),
#                 "sources": processed_data.get('sources', []),
#                 "citation_mapping": processed_data.get('citation_mapping', {}),
#                 "citations": [citation.dict() for citation in citations],
#                 "source_count": len(citations),
#                 "status": "success"
#             }
            
#             return json.dumps(research_result, ensure_ascii=False, indent=2)
            
#         except Exception as e:
#             logger.error(f"Legal research tool failed: {str(e)}")
#             error_result = {
#                 "response_text": f"अनुसन्धान असफल भयो: {str(e)}",
#                 "citations": [],
#                 "source_count": 0,
#                 "status": "error"
#             }
#             return json.dumps(error_result, ensure_ascii=False)
    
#     return legal_research_tool

# def format_final_response(response_text: str, citations: List[CitationSource], query: str) -> str:
#     """Format the final markdown response"""
    
#     # Clean response text
#     clean_response = response_text.strip()
    
#     # Build markdown response
#     markdown_response = f"# {query}\n\n"
    
#     # Add the AI-generated response (which should already be formatted)
#     markdown_response += clean_response
    
#     # Add references section if we have citations
#     if citations:
#         markdown_response += "\n\n## सन्दर्भ सामग्री\n\n"
#         for citation in citations:
#             ref_line = f"[{citation.citation_id}] **{citation.title}**"
#             if citation.document_name:
#                 ref_line += f" ({citation.document_name}"
#                 if citation.page_number:
#                     ref_line += f", पृष्ठ {citation.page_number}"
#                 ref_line += ")"
            
#             markdown_response += f"{ref_line}\n"
#             markdown_response += f"   - {citation.summary[:200]}{'...' if len(citation.summary) > 200 else ''}\n\n"
    
#     return markdown_response

# def create_legal_agent_graph(current_user: UserTenantDB) -> StateGraph:
#     """Create the LangGraph agent for legal research"""
    
#     # Create the research tool
#     research_tool = create_legal_research_tool(current_user)
    
#     # Create LLM
#     llm = ChatOpenAI(
#         model="gpt-4o-mini",
#         api_key=current_user.llm.openai.api_key,
#         temperature=0.1
#     )
    
#     # Bind tools to LLM
#     llm_with_tools = llm.bind_tools([research_tool])
    
#     # Create system prompt
#     system_prompt = """You are an expert Nepali legal researcher. Your task is to:

# 1. Use the legal_research_tool to search for relevant legal information
# 2. Analyze the results and provide comprehensive responses
# 3. Format responses in proper Nepali markdown with clear sections
# 4. Preserve all citations exactly as provided by the tool
# 5. Do NOT create fake URLs or source links
# 6. Only use actual source information from the research results

# When you receive tool results:
# - Extract the response_text and citations
# - Format them into a proper markdown structure
# - Include all citation sources in the references section
# - Ensure citations [1], [2], etc. match the provided sources

# Always respond in Nepali and maintain professional legal language."""
    
#     async def research_node(state: LegalAgentState):
#         """Node that performs legal research"""
#         messages = state["messages"]
#         query = state["query"]
        
#         # Create message with tool call
#         response = await llm_with_tools.ainvoke([
#             SystemMessage(content=system_prompt),
#             HumanMessage(content=f"Research this legal query thoroughly: {query}")
#         ])
        
#         return {"messages": messages + [response]}
    
#     async def tool_node(state: LegalAgentState):
#         """Node that executes tools"""
#         messages = state["messages"]
#         last_message = messages[-1]
        
#         # Execute tool calls
#         tool_results = []
#         if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
#             for tool_call in last_message.tool_calls:
#                 if tool_call["name"] == "legal_research_tool":
#                     result = await research_tool.ainvoke(tool_call["args"])
#                     tool_results.append(ToolMessage(
#                         content=result,
#                         tool_call_id=tool_call["id"]
#                     ))
        
#         return {"messages": messages + tool_results}
    
#     async def format_response_node(state: LegalAgentState):
#         """Node that formats the final response"""
#         messages = state["messages"]
#         query = state["query"]
        
#         # Find the tool result
#         tool_result = None
#         for msg in reversed(messages):
#             if isinstance(msg, ToolMessage):
#                 try:
#                     tool_result = json.loads(msg.content)
#                     break
#                 except:
#                     continue
        
#         if not tool_result:
#             return {
#                 "messages": messages,
#                 "final_response": "कुनै परिणाम फेला परेन।",
#                 "citations": [],
#                 "source_count": 0
#             }
        
#         # Extract data from tool result
#         response_text = tool_result.get("response_text", "")
#         citations_data = tool_result.get("citations", [])
#         source_count = tool_result.get("source_count", 0)
        
#         # Convert citations data back to objects
#         citations = [CitationSource(**cite_data) for cite_data in citations_data]
        
#         # Format final response
#         final_response = format_final_response(response_text, citations, query)
        
#         # Create final AI message
#         final_message = AIMessage(content=final_response)
        
#         return {
#             "messages": messages + [final_message],
#             "final_response": final_response,
#             "citations": citations,
#             "source_count": source_count
#         }
    
#     async def should_continue(state: LegalAgentState):
#         """Decide whether to continue or end"""
#         messages = state["messages"]
#         last_message = messages[-1]
        
#         # If last message has tool calls, go to tool node
#         if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
#             return "tools"
#         else:
#             return "format"
    
#     # Build the graph
#     workflow = StateGraph(LegalAgentState)
    
#     # Add nodes
#     workflow.add_node("research", research_node)
#     workflow.add_node("tools", tool_node)
#     workflow.add_node("format", format_response_node)
    
#     # Add edges
#     workflow.add_edge(START, "research")
#     workflow.add_conditional_edges("research", should_continue, {
#         "tools": "tools",
#         "format": "format"
#     })
#     workflow.add_edge("tools", "format")
#     workflow.add_edge("format", END)
    
#     return workflow.compile()

# @router.post("/langchain-research", response_model=Dict[str, Any])
# async def langchain_legal_research(
#     query: str,
#     current_user: UserTenantDB = Depends(get_tenant_info)
# ) -> Dict[str, Any]:
#     """Execute legal research using LangChain 2025 agent with LlamaIndex retriever"""
    
#     try:
#         # Validate input
#         if not query or not query.strip():
#             return {
#                 "response": "कृपया मान्य प्रश्न प्रदान गर्नुहोस्",
#                 "citations": [],
#                 "query": query,
#                 "status": "error",
#                 "source_count": 0
#             }
        
#         logger.info(f"Starting LangChain legal research for: {query.strip()}")
        
#         # Create agent graph
#         agent_graph = create_legal_agent_graph(current_user)
        
#         # Initial state
#         initial_state = {
#             "messages": [],
#             "query": query.strip(),
#             "citations": [],
#             "final_response": "",
#             "source_count": 0
#         }
        
#         # Run the agent (now fully async)
#         result = await agent_graph.ainvoke(initial_state)
        
#         # Format response
#         citations_data = []
#         if result.get("citations"):
#             citations_data = [citation.dict() if hasattr(citation, 'dict') else citation 
#                             for citation in result["citations"]]
        
#         response = {
#             "response": result.get("final_response", "कुनै जवाफ उत्पन्न भएन"),
#             "citations": citations_data,
#             "query": query.strip(),
#             "status": "success",
#             "source_count": result.get("source_count", 0)
#         }
        
#         logger.info(f"LangChain research completed successfully with {response['source_count']} sources")
#         return response
        
#     except Exception as e:
#         logger.error(f"LangChain research failed: {str(e)}")
#         return {
#             "response": f"अनुसन्धान असफल भयो: {str(e)}",
#             "citations": [],
#             "query": query,
#             "status": "error",
#             "source_count": 0
#         }