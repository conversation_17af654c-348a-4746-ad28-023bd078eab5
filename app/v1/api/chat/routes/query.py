from fastapi import APIRouter, Depends, HTTPException
from app.core.security import get_tenant_info
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from typing import List, Dict, Any
import time
from llama_index.core import VectorStoreIndex
from llama_index.core.schema import NodeWithScore
from collections import defaultdict
from bson import ObjectId


router = APIRouter()
logger = StructuredLogger(__name__)


@router.get("/query", response_model=Dict[str, Any])
async def query_engine(query: str, current_user: CurrentUser = Depends(get_tenant_info)):
    """Query engine for legal documents using LLM and retrieval"""

    try:
        logger.log(f"Query engine request: '{query}' from user {current_user.user.id}")

        # Check if Qdrant index is available
        if not current_user.qd_index:
            logger.exception(f"Qdrant index not available for user {current_user.user.id}")
            raise HTTPException(
                status_code=503,
                detail="Qdrant index not available"
            )

        # Check if LLM is available
        if not current_user.llm or not current_user.llm.openai:
            logger.exception(f"OpenAI LLM not available for user {current_user.user.id}")
            raise HTTPException(
                status_code=503,
                detail="LLM not available"
            )

        logger.debug(f"Creating query engine for user {current_user.user.id}")

        # Create query engine from the index
        query_engine = current_user.qd_index.as_query_engine(
            llm=current_user.llm.openai,
            similarity_top_k=5,  # Get more context for LLM
            response_mode="tree_summarize"  # Better for legal documents
        )

        # Execute query
        logger.test(f"Executing query: '{query}'")
        query_start = time.time()
        response = await query_engine.aquery(query)
        query_duration = time.time() - query_start

        reply= response.response if hasattr(response, 'response') else "No response"
        logger.log_query(query, len(response.source_nodes), query_duration)

        # Process source nodes for detailed response
        logger.debug(f"Processing {len(response.source_nodes)} source nodes")

        articles_dict = defaultdict(list)
        source_nodes: List[NodeWithScore] = response.source_nodes

        for node in source_nodes:
            metadata = node.metadata if hasattr(node, 'metadata') else {}
            article_id = metadata.get('article_mongo_id', 'unknown')

            chunk_data = {
                "chunk_id": node.node_id,
                "score": round(node.score, 4) if hasattr(node, 'score') else 0.0,
                "text": node.text,
                "metadata": metadata
            }

            articles_dict[article_id].append(chunk_data)

        # Sort chunks within each article by score (highest first)
        for article_id in articles_dict:
            articles_dict[article_id].sort(key=lambda x: x['score'], reverse=True)

        logger.debug(f"Grouped nodes into {len(articles_dict)} articles")

        # Convert to list format and fetch complete article data
        sources = []
        for article_id, chunks in articles_dict.items():
            # Skip unknown articles
            if article_id == 'unknown':
                continue
    
            # Fetch complete article from documents collection
            try:
                from bson import ObjectId
                article_doc = await current_user.read_db.documents.find_one({"_id": ObjectId(article_id)})
                if not article_doc:
                    logger.issue(f"Article with ID {article_id} not found")
                    continue

                # Convert ObjectId to string for JSON serialization
                article_metadata = {}
                for key, value in article_doc.items():
                    if key == '_id':
                        article_metadata['_id'] = str(value)
                    else:
                        article_metadata[key] = value

                article_data = {
                    "article_mongo_id": article_id,
                    "metadata": article_metadata,
                    "chunks": chunks
                }

                sources.append(article_data)

            except Exception as e:
                logger.exception(f"Error fetching article {article_id}: {str(e)}")
                # Fallback data
                article_data = {
                    "article_mongo_id": article_id,
                    "metadata": {"error": f"Error loading article: {str(e)}"},
                    "chunks": chunks
                }
                sources.append(article_data)

        # Sort articles by best chunk score
        sources.sort(key=lambda x: max(chunk['score'] for chunk in x['chunks']), reverse=True)

        # Generate response summary
        total_chunks = sum(len(chunks) for chunks in articles_dict.values())
        response = f"Found {total_chunks} relevant chunks across {len(sources)} articles for query: '{query}'"

        return {
            "response": reply,
            "sources": sources
        }

    except Exception as e:
        import traceback
        print(traceback.format_exc())
        logger.exception(f"Query failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Query failed: {str(e)}"
        )

