"""
Multi-Hop Search Engine - Performs intelligent multi-hop reasoning
Uses LLM for concept extraction and hop planning
"""

from typing import Dict, List, Any, Set
import time

from app.core.logger import StructuredLogger
from .query import QueryProcessor

logger = StructuredLogger(__name__)


class MultiHopSearchEngine:
    """
    Performs multi-hop search with intelligent concept extraction
    Uses LLM strategically for concept identification and hop planning
    """
    
    def __init__(self, user_tenant_db):
        self.user_db = user_tenant_db
        self.llm = user_tenant_db.llm.openai if user_tenant_db.llm else None
        self.query_processor = QueryProcessor(user_tenant_db)
        
        # Track concepts to avoid redundant hops
        self.explored_concepts = set()
        self.query_cache = {}
    
    async def search(self, query: str, max_hops: int = 3) -> Dict[str, Any]:
        """
        Perform multi-hop search with intelligent concept extraction
        """
        start_time = time.time()
        logger.log(f"Multi-hop search starting: '{query}' (max_hops: {max_hops})")
        
        all_source_nodes = []
        reasoning_steps = []
        self.explored_concepts.clear()
        
        try:
            # Hop 0: Initial search
            initial_result = await self.query_processor.adaptive_search(query)
            initial_nodes = initial_result.get("source_nodes", [])
            all_source_nodes.extend(initial_nodes)
            
            reasoning_steps.append(f"Initial search: Found {len(initial_nodes)} sources")
            reasoning_steps.extend(initial_result.get("reasoning_steps", []))
            
            if not initial_nodes:
                logger.warning("No initial results found")
                return self._create_result(query, all_source_nodes, reasoning_steps, start_time)
            
            # Multi-hop expansion
            for hop in range(1, max_hops):
                logger.debug(f"Starting hop {hop}/{max_hops-1}")
                
                # Extract concepts for next hop
                hop_concepts = await self._extract_hop_concepts(initial_nodes if hop == 1 else hop_nodes)
                
                if not hop_concepts:
                    reasoning_steps.append(f"Hop {hop}: No new concepts found, stopping")
                    break
                
                # Perform hop searches
                hop_nodes = []
                for concept in hop_concepts:
                    if concept in self.explored_concepts:
                        continue
                    
                    concept_query = f"{query} {concept}"
                    
                    # Check cache first
                    if concept_query in self.query_cache:
                        cached_nodes = self.query_cache[concept_query]
                        hop_nodes.extend(cached_nodes)
                        reasoning_steps.append(f"Hop {hop}: Used cached results for '{concept}'")
                        continue
                    
                    try:
                        concept_result = await self.query_processor.citation_search(concept_query)
                        concept_nodes = concept_result.get("source_nodes", [])
                        
                        if concept_nodes:
                            hop_nodes.extend(concept_nodes)
                            self.query_cache[concept_query] = concept_nodes
                            reasoning_steps.append(f"Hop {hop}: '{concept}' -> {len(concept_nodes)} sources")
                        
                        self.explored_concepts.add(concept)
                        
                    except Exception as e:
                        logger.warning(f"Hop search failed for concept '{concept}': {e}")
                        continue
                
                if hop_nodes:
                    # Remove duplicates
                    new_nodes = self._deduplicate_nodes(hop_nodes, all_source_nodes)
                    all_source_nodes.extend(new_nodes)
                    reasoning_steps.append(f"Hop {hop}: Added {len(new_nodes)} new sources")
                else:
                    reasoning_steps.append(f"Hop {hop}: No new sources found")
                    break
            
            # Generate final answer
            final_answer = await self._synthesize_answer(query, all_source_nodes)
            
            result = self._create_result(query, all_source_nodes, reasoning_steps, start_time)
            result["answer"] = final_answer
            
            logger.log(f"Multi-hop search completed: {len(all_source_nodes)} total sources")
            return result
            
        except Exception as e:
            logger.exception(f"Multi-hop search failed: {e}")
            return self._create_result(query, all_source_nodes, reasoning_steps, start_time, error=str(e))
    
    async def _extract_hop_concepts(self, source_nodes: List) -> List[str]:
        """
        Extract concepts for next hop using LLM when available
        """
        if not source_nodes:
            return []
        
        # Combine text from top source nodes
        combined_text = ""
        for node in source_nodes[:3]:  # Use top 3 nodes
            combined_text += node.text[:200] + " "
        
        if self.llm:
            # Use LLM for intelligent concept extraction
            return await self._llm_extract_concepts(combined_text)
        else:
            # Fallback to rule-based extraction
            return self.query_processor.extract_legal_concepts(combined_text)
    
    async def _llm_extract_concepts(self, text: str) -> List[str]:
        """
        Use LLM to extract relevant legal concepts for next hop
        """
        if not self.llm:
            return []
        
        prompt = f"""
        From this legal text, extract 3-5 key legal concepts that would be useful for finding related legal documents:
        
        Text: {text[:500]}...
        
        Focus on:
        - Legal principles and doctrines
        - Related areas of law
        - Key legal terms that appear
        
        Return only the concepts, one per line, no explanations.
        """
        
        try:
            response = await self.llm.acomplete(prompt)
            concepts = [line.strip() for line in response.text.split('\n') if line.strip()]
            
            # Filter out concepts we've already explored
            new_concepts = [c for c in concepts if c not in self.explored_concepts]
            
            return new_concepts[:3]  # Limit to 3 concepts per hop
            
        except Exception as e:
            logger.warning(f"LLM concept extraction failed: {e}")
            return self.query_processor.extract_legal_concepts(text)
    
    def _deduplicate_nodes(self, new_nodes: List, existing_nodes: List) -> List:
        """
        Remove duplicate nodes based on content similarity
        """
        if not existing_nodes:
            return new_nodes
        
        # Get existing node IDs
        existing_ids = set()
        for node in existing_nodes:
            node_id = getattr(node, 'node_id', None)
            if node_id:
                existing_ids.add(node_id)
        
        # Filter out duplicates
        unique_nodes = []
        for node in new_nodes:
            node_id = getattr(node, 'node_id', None)
            if node_id and node_id not in existing_ids:
                unique_nodes.append(node)
                existing_ids.add(node_id)
        
        return unique_nodes
    
    async def _synthesize_answer(self, query: str, source_nodes: List) -> str:
        """
        Synthesize final answer from all source nodes
        """
        if not source_nodes:
            return "No relevant information found."
        
        if self.llm:
            # Use LLM for intelligent synthesis
            return await self._llm_synthesize_answer(query, source_nodes)
        else:
            # Simple concatenation fallback
            return f"Based on {len(source_nodes)} legal sources found for: {query}"
    
    async def _llm_synthesize_answer(self, query: str, source_nodes: List) -> str:
        """
        Use LLM to synthesize comprehensive answer from multiple sources
        """
        if not self.llm:
            return "LLM not available for synthesis"
        
        # Prepare source summaries
        source_summaries = []
        for i, node in enumerate(source_nodes[:8]):  # Use top 8 sources
            summary = f"[{i+1}] {node.text[:300]}..."
            source_summaries.append(summary)
        
        prompt = f"""
        Based on the following legal sources, provide a comprehensive answer to: "{query}"
        
        Sources:
        {chr(10).join(source_summaries)}
        
        Requirements:
        1. Provide a clear, structured answer
        2. Use [1], [2] etc. to reference sources
        3. Explain the legal reasoning
        4. Note any conflicting information
        5. Keep the answer focused and relevant
        """
        
        try:
            response = await self.llm.acomplete(prompt)
            return response.text
        except Exception as e:
            logger.warning(f"LLM synthesis failed: {e}")
            return f"Found {len(source_nodes)} relevant legal sources for: {query}"

    def _create_result(self, query: str, source_nodes: List, reasoning_steps: List,
                      start_time: float, error: str = None) -> Dict[str, Any]:
        """
        Create standardized result dictionary
        """
        processing_time = time.time() - start_time

        return {
            "query": query,
            "answer": f"Error: {error}" if error else "Multi-hop search completed",
            "source_nodes": source_nodes,
            "reasoning_steps": reasoning_steps,
            "processing_time": processing_time,
            "total_sources": len(source_nodes),
            "hops_performed": len([step for step in reasoning_steps if "Hop" in step]),
            "error": error
        }
