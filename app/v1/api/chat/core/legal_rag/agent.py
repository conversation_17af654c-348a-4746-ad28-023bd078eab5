"""
Legal RAG Agent - Orchestrates the entire legal research process
Uses LLM for intelligent decision making and query planning
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import time

from llama_index.core.agent import ReActAgent
from llama_index.core.tools import QueryEngineTool, ToolMetadata
from llama_index.core.llms import LLM

from app.core.logger import StructuredLogger
from .query import QueryProcessor
from .multi_hop import MultiHopSearchEngine
from .citation import CitationManager

logger = StructuredLogger(__name__)


@dataclass
class AgentResult:
    """Result from agent-orchestrated legal research"""
    query: str
    answer: str
    confidence_score: float
    reasoning_steps: List[str]
    source_groups: List[Dict[str, Any]]
    total_sources: int
    unique_articles: int
    processing_time: float
    agent_decisions: List[str]


class LegalRAGAgent:
    """
    Main agent that orchestrates legal research using LLM for intelligent decisions
    Coordinates query processing, multi-hop search, and citation management
    """
    
    def __init__(self, user_tenant_db):
        self.user_db = user_tenant_db
        self.llm = user_tenant_db.llm.openai if user_tenant_db.llm else None
        
        # Initialize components
        self.query_processor = QueryProcessor(user_tenant_db)
        self.multi_hop_engine = MultiHopSearchEngine(user_tenant_db)
        self.citation_manager = CitationManager()
        
        # Create tools for agent
        self.tools = self._create_agent_tools()
        
        # Initialize ReAct agent if LLM available
        self.agent = None
        if self.llm:
            self.agent = ReActAgent.from_tools(
                tools=self.tools,
                llm=self.llm,
                verbose=True,
                max_iterations=4,  # Limit to control LLM usage
                system_prompt=self._get_system_prompt()
            )
        
        # Track agent decisions for transparency
        self.agent_decisions = []
    
    def _create_agent_tools(self) -> List[QueryEngineTool]:
        """Create tools for the agent to use"""
        tools = []
        
        # Legal search tool
        legal_search_tool = QueryEngineTool(
            query_engine=self.query_processor.citation_query_engine,
            metadata=ToolMetadata(
                name="legal_search",
                description="Search legal documents with citations. Use for finding relevant legal information."
            )
        )
        tools.append(legal_search_tool)
        
        return tools
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for the agent"""
        return """
        You are a legal research agent. Your job is to:
        1. Analyze the user's legal query
        2. Determine if multi-hop search is needed
        3. Plan the search strategy efficiently
        4. Coordinate the research process
        
        Be efficient - only use multi-hop search when the initial query doesn't provide sufficient information.
        Focus on finding the most relevant legal sources and precedents.
        """
    
    async def research(self, query: str, max_hops: int = 3) -> AgentResult:
        """
        Main research method - agent orchestrates the entire process
        """
        start_time = time.time()
        self.agent_decisions = []
        
        logger.log(f"Agent starting legal research for: '{query}'")
        
        try:
            # Step 1: Agent analyzes query and plans approach
            if self.agent:
                planning_result = await self._agent_plan_research(query)
                self.agent_decisions.append(f"Planning: {planning_result}")
            else:
                planning_result = {"strategy": "direct_search", "needs_multi_hop": True}
            
            # Step 2: Execute search based on agent's plan
            if planning_result.get("needs_multi_hop", True):
                search_result = await self.multi_hop_engine.search(query, max_hops)
                self.agent_decisions.append("Decision: Using multi-hop search")
            else:
                search_result = await self.query_processor.simple_search(query)
                self.agent_decisions.append("Decision: Using simple search")
            
            # Step 3: Agent synthesizes final answer
            if self.agent and search_result.get("source_nodes"):
                final_answer = await self._agent_synthesize_answer(query, search_result)
                self.agent_decisions.append("Synthesis: Agent combined multiple sources")
            else:
                final_answer = search_result.get("answer", "No relevant information found")
            
            # Step 4: Group and organize sources
            source_groups = self.citation_manager.group_sources(
                search_result.get("source_nodes", [])
            )
            
            # Step 5: Calculate confidence
            confidence_score = self._calculate_confidence(search_result, source_groups)
            
            processing_time = time.time() - start_time
            
            result = AgentResult(
                query=query,
                answer=final_answer,
                confidence_score=confidence_score,
                reasoning_steps=search_result.get("reasoning_steps", []),
                source_groups=source_groups,
                total_sources=len(search_result.get("source_nodes", [])),
                unique_articles=len(source_groups),
                processing_time=processing_time,
                agent_decisions=self.agent_decisions
            )
            
            logger.log(f"Agent research completed in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.exception(f"Agent research failed: {e}")
            raise
    
    async def _agent_plan_research(self, query: str) -> Dict[str, Any]:
        """Agent plans the research strategy"""
        if not self.agent:
            return {"strategy": "direct_search", "needs_multi_hop": True}
        
        planning_prompt = f"""
        Analyze this legal query and determine the best research strategy:
        Query: "{query}"
        
        Consider:
        1. Is this a simple factual query or complex legal analysis?
        2. Will multi-hop search help find related precedents?
        3. What legal concepts should be explored?
        
        Respond with: SIMPLE_SEARCH or MULTI_HOP_SEARCH and explain why.
        """
        
        try:
            response = await self.agent.achat(planning_prompt)
            needs_multi_hop = "MULTI_HOP" in response.response.upper()
            
            return {
                "strategy": "multi_hop" if needs_multi_hop else "simple",
                "needs_multi_hop": needs_multi_hop,
                "reasoning": response.response
            }
        except Exception as e:
            logger.warning(f"Agent planning failed: {e}")
            return {"strategy": "multi_hop", "needs_multi_hop": True}
    
    async def _agent_synthesize_answer(self, query: str, search_result: Dict) -> str:
        """Agent synthesizes final answer from multiple sources"""
        if not self.agent:
            return search_result.get("answer", "No information found")
        
        # Prepare source summaries for synthesis
        source_summaries = []
        for i, node in enumerate(search_result.get("source_nodes", [])[:5]):
            summary = f"Source {i+1}: {node.text[:200]}..."
            source_summaries.append(summary)
        
        synthesis_prompt = f"""
        Based on the legal research findings, provide a comprehensive answer to: "{query}"
        
        Sources found:
        {chr(10).join(source_summaries)}
        
        Provide a well-structured legal analysis that:
        1. Directly answers the query
        2. References relevant sources with [1], [2] notation
        3. Explains the legal reasoning
        4. Notes any limitations or areas needing further research
        """
        
        try:
            response = await self.agent.achat(synthesis_prompt)
            return response.response
        except Exception as e:
            logger.warning(f"Agent synthesis failed: {e}")
            return search_result.get("answer", "Synthesis failed")
    
    def _calculate_confidence(self, search_result: Dict, source_groups: List) -> float:
        """Calculate confidence based on search results and source quality"""
        base_confidence = 0.3
        
        # Boost for number of sources
        source_count = len(search_result.get("source_nodes", []))
        source_boost = min(0.4, source_count * 0.05)
        
        # Boost for source diversity (unique articles)
        diversity_boost = min(0.2, len(source_groups) * 0.04)
        
        # Boost for agent involvement
        agent_boost = 0.1 if self.agent else 0.0
        
        total_confidence = base_confidence + source_boost + diversity_boost + agent_boost
        return min(1.0, total_confidence)
