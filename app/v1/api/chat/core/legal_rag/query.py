"""
Query Processing - Handles query expansion and CitationQueryEngine operations
"""

from typing import Dict, List, Any, Optional
import time

from llama_index.core.query_engine import CitationQueryEngine
from llama_index.core.schema import NodeWithScore

from app.core.logger import StructuredLogger

logger = StructuredLogger(__name__)


class QueryProcessor:
    """
    Handles query processing and CitationQueryEngine operations
    Manages query expansion and basic search functionality
    """
    
    def __init__(self, user_tenant_db):
        self.user_db = user_tenant_db
        self.llm = user_tenant_db.llm.openai if user_tenant_db.llm else None
        
        # Create CitationQueryEngine
        self.citation_query_engine = CitationQueryEngine.from_args(
            index=user_tenant_db.qd_index,
            similarity_top_k=12,
            citation_chunk_size=512,
            llm=self.llm
        )
        
        # Legal term mappings for query expansion
        self.legal_synonyms = {
            "contract": ["agreement", "covenant", "pact", "संझौता"],
            "negligence": ["carelessness", "breach of duty", "लापरवाही"],
            "damages": ["compensation", "remedy", "क्षतिपूर्ति"],
            "court": ["tribunal", "judiciary", "अदालत", "न्यायालय"],
            "judge": ["justice", "magistrate", "न्यायाधीश"],
            "law": ["statute", "regulation", "कानून", "ऐन"],
            "case": ["lawsuit", "matter", "मुद्दा"],
            "decision": ["judgment", "ruling", "निर्णय", "फैसला"]
        }
    
    def expand_query(self, query: str) -> List[str]:
        """
        Expand query with legal synonyms (no LLM usage for efficiency)
        """
        expanded_queries = [query]
        
        # Add synonym-based expansions
        query_lower = query.lower()
        for term, synonyms in self.legal_synonyms.items():
            if term in query_lower:
                for synonym in synonyms[:2]:  # Limit to 2 synonyms per term
                    expanded_query = query.replace(term, synonym)
                    if expanded_query != query:
                        expanded_queries.append(expanded_query)
        
        # Remove duplicates and limit total expansions
        unique_queries = list(dict.fromkeys(expanded_queries))
        return unique_queries[:4]  # Limit to 4 total queries
    
    async def simple_search(self, query: str) -> Dict[str, Any]:
        """
        Perform simple search using CitationQueryEngine
        """
        logger.debug(f"Simple search for: {query}")
        
        try:
            # Expand query
            expanded_queries = self.expand_query(query)
            
            # Use the best expanded query (first one is original)
            search_query = expanded_queries[0]
            
            # Perform search
            response = await self.citation_query_engine.aquery(search_query)
            
            return {
                "answer": response.response,
                "source_nodes": response.source_nodes,
                "reasoning_steps": [f"Simple search: '{search_query}'"],
                "query_expansions": expanded_queries
            }
            
        except Exception as e:
            logger.error(f"Simple search failed: {e}")
            return {
                "answer": f"Search failed: {e}",
                "source_nodes": [],
                "reasoning_steps": ["Search failed"],
                "query_expansions": [query]
            }
    
    async def citation_search(self, query: str) -> Dict[str, Any]:
        """
        Perform citation-based search with source attribution
        """
        logger.debug(f"Citation search for: {query}")
        
        try:
            response = await self.citation_query_engine.aquery(query)
            
            # Extract citation information
            citations = []
            for i, source_node in enumerate(response.source_nodes):
                citation = {
                    "id": f"[{i+1}]",
                    "text": source_node.text,
                    "score": source_node.score,
                    "metadata": source_node.metadata,
                    "article_id": source_node.metadata.get("article_mongo_id"),
                    "title": source_node.metadata.get("title", "Unknown"),
                    "court": source_node.metadata.get("court_type"),
                    "year": source_node.metadata.get("year")
                }
                citations.append(citation)
            
            return {
                "answer": response.response,
                "source_nodes": response.source_nodes,
                "citations": citations,
                "reasoning_steps": [f"Citation search: '{query}'"]
            }
            
        except Exception as e:
            logger.error(f"Citation search failed: {e}")
            return {
                "answer": f"Citation search failed: {e}",
                "source_nodes": [],
                "citations": [],
                "reasoning_steps": ["Citation search failed"]
            }
    
    def extract_legal_concepts(self, text: str) -> List[str]:
        """
        Extract legal concepts from text without LLM (for efficiency)
        """
        legal_keywords = [
            'contract', 'agreement', 'breach', 'damages', 'liability', 'negligence',
            'statute', 'regulation', 'precedent', 'court', 'judge', 'plaintiff', 
            'defendant', 'evidence', 'appeal', 'jurisdiction', 'remedy',
            'अदालत', 'कानून', 'न्यायाधीश', 'निर्णय', 'मुद्दा', 'फैसला'
        ]
        
        found_concepts = []
        text_lower = text.lower()
        
        for keyword in legal_keywords:
            if keyword.lower() in text_lower:
                found_concepts.append(keyword)
        
        # Remove duplicates and limit
        return list(dict.fromkeys(found_concepts))[:5]
    
    def should_expand_query(self, query: str, initial_results: List[NodeWithScore]) -> bool:
        """
        Determine if query should be expanded based on initial results
        """
        # Expand if we have few results
        if len(initial_results) < 3:
            return True
        
        # Expand if average relevance is low
        avg_score = sum(node.score or 0 for node in initial_results) / len(initial_results)
        if avg_score < 0.7:
            return True
        
        # Don't expand if we have good results
        return False
    
    async def adaptive_search(self, query: str) -> Dict[str, Any]:
        """
        Adaptive search that decides whether to expand based on initial results
        """
        logger.debug(f"Adaptive search for: {query}")
        
        # Initial search
        initial_result = await self.simple_search(query)
        initial_nodes = initial_result.get("source_nodes", [])
        
        # Check if we need to expand
        if self.should_expand_query(query, initial_nodes):
            logger.debug("Expanding query due to insufficient results")
            
            # Try expanded queries
            expanded_queries = self.expand_query(query)
            all_nodes = initial_nodes.copy()
            reasoning_steps = initial_result.get("reasoning_steps", [])
            
            for expanded_query in expanded_queries[1:]:  # Skip original query
                try:
                    expanded_result = await self.simple_search(expanded_query)
                    expanded_nodes = expanded_result.get("source_nodes", [])
                    all_nodes.extend(expanded_nodes)
                    reasoning_steps.append(f"Expanded search: '{expanded_query}'")
                except Exception as e:
                    logger.warning(f"Expanded search failed for '{expanded_query}': {e}")
            
            # Remove duplicates based on node_id
            seen_ids = set()
            unique_nodes = []
            for node in all_nodes:
                node_id = getattr(node, 'node_id', str(node))
                if node_id not in seen_ids:
                    seen_ids.add(node_id)
                    unique_nodes.append(node)
            
            return {
                "answer": initial_result.get("answer", ""),
                "source_nodes": unique_nodes,
                "reasoning_steps": reasoning_steps,
                "expanded": True
            }
        else:
            logger.debug("Using initial results - no expansion needed")
            return {
                **initial_result,
                "expanded": False
            }
