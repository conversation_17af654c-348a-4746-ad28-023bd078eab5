"""
Citation Manager - Handles source grouping and citation organization
Groups sources by article to reduce duplication and improve source attribution
"""

from typing import Dict, List, Any, Optional
from collections import defaultdict
from dataclasses import dataclass

from app.core.logger import StructuredLogger

logger = StructuredLogger(__name__)


@dataclass
class SourceGroup:
    """Groups sources by article for better organization and deduplication"""
    article_id: str
    article_title: str
    article_url: Optional[str]
    court_type: Optional[str]
    year: Optional[int]
    chunks: List[Dict[str, Any]]
    total_relevance: float
    chunk_count: int
    
    def add_chunk(self, chunk_data: Dict[str, Any]):
        """Add a chunk to this source group"""
        self.chunks.append(chunk_data)
        self.total_relevance += chunk_data.get('relevance_score', 0.0)
        self.chunk_count += 1
    
    def get_average_relevance(self) -> float:
        """Get average relevance score for this source group"""
        return self.total_relevance / self.chunk_count if self.chunk_count > 0 else 0.0
    
    def get_best_chunk(self) -> Dict[str, Any]:
        """Get the most relevant chunk from this source"""
        if not self.chunks:
            return {}
        return max(self.chunks, key=lambda x: x.get('relevance_score', 0.0))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response"""
        return {
            "article_id": self.article_id,
            "article_title": self.article_title,
            "article_url": self.article_url,
            "court_type": self.court_type,
            "year": self.year,
            "chunk_count": self.chunk_count,
            "average_relevance": self.get_average_relevance(),
            "best_chunk": self.get_best_chunk(),
            "all_chunks": self.chunks
        }


class CitationManager:
    """
    Manages citation organization and source grouping
    Groups sources by article to reduce duplication and improve attribution
    """
    
    def __init__(self):
        self.source_groups = {}
    
    def group_sources(self, source_nodes: List) -> List[Dict[str, Any]]:
        """
        Group source nodes by article ID to reduce duplication
        """
        if not source_nodes:
            return []
        
        logger.debug(f"Grouping {len(source_nodes)} source nodes")
        
        # Group by article ID
        article_groups = defaultdict(list)
        
        for i, node in enumerate(source_nodes):
            # Extract metadata
            metadata = getattr(node, 'metadata', {})
            article_id = metadata.get('article_mongo_id', f'unknown_{i}')
            
            # Create chunk data
            chunk_data = {
                "chunk_id": f"chunk_{i}",
                "text": getattr(node, 'text', ''),
                "relevance_score": getattr(node, 'score', 0.0),
                "metadata": metadata
            }
            
            article_groups[article_id].append(chunk_data)
        
        # Create SourceGroup objects
        source_groups = []
        for article_id, chunks in article_groups.items():
            # Get article metadata from first chunk
            first_chunk = chunks[0]
            metadata = first_chunk.get('metadata', {})
            
            source_group = SourceGroup(
                article_id=article_id,
                article_title=metadata.get('title', 'Unknown Document'),
                article_url=metadata.get('url'),
                court_type=metadata.get('court_type'),
                year=metadata.get('year'),
                chunks=[],
                total_relevance=0.0,
                chunk_count=0
            )
            
            # Add all chunks to the group
            for chunk in chunks:
                source_group.add_chunk(chunk)
            
            source_groups.append(source_group)
        
        # Sort by average relevance
        source_groups.sort(key=lambda x: x.get_average_relevance(), reverse=True)
        
        logger.debug(f"Created {len(source_groups)} source groups")
        
        # Convert to dictionaries for API response
        return [group.to_dict() for group in source_groups]
    
    def create_citations(self, source_groups: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create citation list with proper numbering and attribution
        """
        citations = []
        citation_counter = 1
        
        for group in source_groups:
            # Create citation for the best chunk from each article
            best_chunk = group.get('best_chunk', {})
            
            if best_chunk:
                citation = {
                    "id": f"[{citation_counter}]",
                    "article_id": group['article_id'],
                    "article_title": group['article_title'],
                    "court_type": group.get('court_type'),
                    "year": group.get('year'),
                    "chunk_text": best_chunk.get('text', '')[:300] + "...",
                    "relevance_score": best_chunk.get('relevance_score', 0.0),
                    "chunk_count": group['chunk_count'],
                    "url": group.get('article_url')
                }
                citations.append(citation)
                citation_counter += 1
        
        return citations
    
    def get_source_statistics(self, source_groups: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get statistics about the sources
        """
        if not source_groups:
            return {
                "total_articles": 0,
                "total_chunks": 0,
                "average_relevance": 0.0,
                "court_types": [],
                "year_range": None
            }
        
        total_chunks = sum(group['chunk_count'] for group in source_groups)
        total_relevance = sum(group['average_relevance'] * group['chunk_count'] for group in source_groups)
        average_relevance = total_relevance / total_chunks if total_chunks > 0 else 0.0
        
        # Get court types
        court_types = list(set(
            group.get('court_type') for group in source_groups 
            if group.get('court_type')
        ))
        
        # Get year range
        years = [group.get('year') for group in source_groups if group.get('year')]
        year_range = None
        if years:
            year_range = {"min": min(years), "max": max(years)}
        
        return {
            "total_articles": len(source_groups),
            "total_chunks": total_chunks,
            "average_relevance": round(average_relevance, 3),
            "court_types": court_types,
            "year_range": year_range
        }
    
    def deduplicate_by_content(self, source_nodes: List, similarity_threshold: float = 0.8) -> List:
        """
        Remove duplicate sources based on content similarity
        """
        if not source_nodes:
            return []
        
        unique_nodes = []
        seen_texts = set()
        
        for node in source_nodes:
            text = getattr(node, 'text', '')
            text_hash = hash(text[:200])  # Use first 200 chars for comparison
            
            if text_hash not in seen_texts:
                seen_texts.add(text_hash)
                unique_nodes.append(node)
        
        logger.debug(f"Deduplicated {len(source_nodes)} -> {len(unique_nodes)} sources")
        return unique_nodes
    
    def filter_by_relevance(self, source_groups: List[Dict[str, Any]], 
                           min_relevance: float = 0.3) -> List[Dict[str, Any]]:
        """
        Filter source groups by minimum relevance threshold
        """
        filtered_groups = [
            group for group in source_groups 
            if group['average_relevance'] >= min_relevance
        ]
        
        logger.debug(f"Filtered {len(source_groups)} -> {len(filtered_groups)} groups by relevance")
        return filtered_groups
