from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core import VectorStoreIndex, ServiceContext
from llama_index.retrievers.bm25 import BM25Retriever
import Stemmer



async def create_bm25_retriever(nodes):
    # We can pass in the index, docstore, or list of nodes to create the retriever
    bm25_retriever = BM25Retriever.from_defaults(
        nodes=nodes,
        similarity_top_k=2,
        # Optional: We can pass in the stemmer and set the language for stopwords
        # This is important for removing stopwords and stemming the query + text
        # The default is english for both
        stemmer=Stemmer.Stemmer("Nepali"),  # Use Nepali stemmer
        language="nepali"  # Set language to Nepali,
    )
    return bm25_retriever