# from app.core.security import get_tenant_info
# from app.models.current_user import CurrentUser
# from app.core.logger import StructuredLogger
# from app.v1.api.chat.core.tools import LegalTools
# from app.v1.api.chat.core.utils.process_najirs import _process_sources

# from typing_extensions import TypedDict
# from langchain_core.messages import HumanMessage, BaseMessage, SystemMessage, AIMessage
# from langchain_openai import ChatOpenAI
# from langchain_mongodb import MongoDBChatMessageHistory
# from langgraph.graph import StateGraph, START
# from langgraph.graph.message import add_messages

# from langgraph.checkpoint.mongodb import AsyncMongoDBSaver
# from fastapi import APIRouter, Depends, Query
# from typing import Annotated, List, Optional, Dict, Any
# import json
# import asyncio
# import time
# from datetime import datetime, timezone
# import urllib.parse

# logger = StructuredLogger(__name__)
# router = APIRouter()

# class AgentState(TypedDict):
#     messages: Annotated[List[BaseMessage], add_messages]
#     original_query: str  # Store original query
#     is_legal_query: bool  # Whether query is legal-related
#     refined_query: str  # Enhanced query with context
#     najir_response: str
#     act_response: str
#     najir_summary_response: str
#     najir_sources: List[dict]
#     act_sources: List[dict]
#     najir_summary_sources: List[dict]
#     final_response: str
#     citation_mapping: Dict[str, Any]  # Final citation mapping
#     step_times: Dict[str, float]

# # Global caches
# _TOOL_CACHE = {}

# def get_current_timestamp():
#     """Get current UTC timestamp in ISO format"""
#     return datetime.now(timezone.utc).isoformat()

# class ChatHistoryManager:
#     """Manages chat history using MongoDB with proper message structure"""
    
#     def __init__(self, current_user: CurrentUser):
#         self.current_user: CurrentUser = current_user
#         logger.info("💬 CHAT HISTORY MANAGER - Initialized")
    
#     async def get_user_history(self, user_id: str) -> MongoDBChatMessageHistory:
#         """Get chat history for specific user"""
#         logger.info(f"📚 GETTING CHAT HISTORY - User: {user_id}")
        
#         # Construct connection string from async client
#         client = self.current_user.db.client
#         address = client.address
#         connection_string = f"mongodb://{address[0]}:{address[1]}"
        
#         return MongoDBChatMessageHistory(
#             connection_string=connection_string,
#             database_name=self.current_user.db.read_db.name,
#             collection_name="chat_history",
#             session_id=user_id
#         )
    
#     async def save_interaction(self, user_id: str, query: str, response: str, tools_used: List[str] = None, step_times: Dict[str, float] = None):
#         """Save user interaction to chat history with proper timestamps and metadata"""
#         logger.info(f"💾 SAVING INTERACTION - User: {user_id}")
        
#         current_time = get_current_timestamp()
        
#         # Create user message with proper structure
#         user_message_data = {
#             "role": "user",
#             "content": query,
#             "sender": "user",
#             "created_at": current_time,
#             "user_id": user_id,
#             "message_type": "query"
#         }
        
#         # Create assistant message with proper structure and tools used
#         assistant_message_data = {
#             "role": "assistant", 
#             "content": response,
#             "sender": "assistant",
#             "created_at": current_time,
#             "tools_used": tools_used or [],
#             "user_id": user_id,
#             "message_type": "response",
#             "step_times": step_times or {},
#             "processing_metadata": {
#                 "total_tools": len(tools_used) if tools_used else 0,
#                 "workflow_completed": True
#             }
#         }
        
#         history = await self.get_user_history(user_id)
        
#         # Add messages with metadata
#         history.add_message(HumanMessage(
#             content=query,
#             additional_kwargs=user_message_data
#         ))
        
#         history.add_message(AIMessage(
#             content=response,
#             additional_kwargs=assistant_message_data
#         ))
        
#         logger.info("✅ INTERACTION SAVED - Chat history updated with timestamps and metadata")

# class LegalAgent:
#     def __init__(self, current_user: CurrentUser):
#         logger.info("🤖 AGENT INIT - Starting LegalAgent initialization")
        
#         self.current_user = current_user
#         self.llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
#         self.tools = []
#         self.checkpointer = AsyncMongoDBSaver(
#             client=current_user.db.async_client,
#             db_name=current_user.db.read_db.name,
#             collection_name="checkpoints"
#         )
#         self.chat_manager = ChatHistoryManager(current_user)
#         self.graph = None
        
#         self.query_analysis_prompt = """
# Analyze this query to determine if it's legal-related and what tools are needed:

# Query: {query}

# Respond with JSON:
# {{
#     "is_legal": true/false,
#     "refined_query": "enhanced query with legal context and synonyms",
#     "needs_najir": true/false,
#     "needs_act": true/false,
#     "needs_summary": true/false,
#     "reasoning": "explanation"
# }}

# Legal indicators:
# - Nepali: कानून, अदालत, मुद्दा, फैसला, ऐन, धारा, ठगी, चोरी, हत्या, जग्गा, सम्पत्ति
# - English: law, court, case, judgment, act, section, legal, crime, theft, fraud, property

# Query refinement examples:
# - "ठगी" → "ठगी धोका fraud cheating दण्ड संहिता"
# - "जग्गा विवाद" → "जग्गा मुद्दा सम्पत्ति विवाद भूमि ऐन property dispute"
# """

#         self.synthesis_prompt = """
# You are given multiple legal reference responses for a user query.  

# Original User Query: {query}

# Sources:  
# - NAJIR Response (Case Law): {najir_response}  
# - ACT Response (Statutes): {act_response}  
# - SUMMARY Response (Legal Summaries): {najir_summary_response}  

# Task:  
# - Carefully read all provided responses.  
# - Synthesize them into one **comprehensive, unified legal analysis**.  
# - The final answer must:  
#   1. Directly address the user's query: "{query}"
#   2. Stay strictly grounded in the provided sources (NAJIR, ACT, SUMMARY).  
#   3. Avoid adding new interpretations, assumptions, or information not present in the sources.  
#   4. Present the analysis clearly, structured, and logically connected.
#   5. If sources are contradictory, acknowledge the differences.
#   6. If sources are insufficient, clearly state the limitations.

# Output:  
# A single, consolidated legal guidance derived entirely from the provided sources in Markdown format.
# Respond in the same language as the user's query.
# """
        
#         logger.info("✅ AGENT INIT COMPLETE")

#     def setup_tools(self):
#         """Setup tools with caching"""
#         user_id = str(self.current_user.user.id)
        
#         if user_id not in _TOOL_CACHE:
#             logger.info(f"🔨 CREATING TOOLS - For user: {user_id}")
#             legal_tools = LegalTools()
#             self.tools = legal_tools.create_tools(self.current_user)
#             _TOOL_CACHE[user_id] = self.tools
#             logger.info(f"✅ TOOLS CACHED - Cached for user: {user_id}")
#         else:
#             logger.info(f"♻️  USING CACHED TOOLS - For user: {user_id}")
#             self.tools = _TOOL_CACHE[user_id]

#     def relevance_check_node(self, state: AgentState):
#         """STEP 1: Check if query is legal-related"""
#         step_start = time.time()
#         logger.info("🔄 STEP 1 START - Legal relevance check")

#         user_query = state.get("original_query", state["messages"][-1].content)
#         logger.info(f"📝 USER QUERY: {user_query}")
#         logger.info(f"🕐 STEP 1 TIMESTAMP: {get_current_timestamp()}")

#         # Check if query is legal-related
#         relevance_prompt = self.query_relevance_prompt.format(query=user_query)
#         relevance_response = self.llm.invoke([SystemMessage(content=relevance_prompt)])

#         try:
#             relevance_data = json.loads(relevance_response.content)
#             is_legal = relevance_data.get("is_legal", False)
#             confidence = relevance_data.get("confidence", 0.0)
#             logger.info(f"🏛️ LEGAL RELEVANCE: {is_legal} (confidence: {confidence:.2f})")
#         except json.JSONDecodeError:
#             logger.warning("Failed to parse relevance check, assuming legal query")
#             is_legal = True
#             relevance_data = {"is_legal": True, "confidence": 0.5, "reasoning": "Parse error fallback"}

#         step_time = time.time() - step_start
#         step_times = state.get("step_times", {})
#         step_times["relevance_check"] = step_time

#         logger.info(f"✅ STEP 1 COMPLETE - Relevance checked in {step_time:.2f}s")

#         return {
#             "is_legal_query": is_legal,
#             "step_times": step_times
#         }

#     def query_refinement_node(self, state: AgentState):
#         """STEP 2: Refine and expand query with context"""
#         step_start = time.time()
#         logger.info("🔄 STEP 2 START - Query refinement")

#         user_query = state.get("original_query", "")

#         # Skip refinement if not legal query
#         if not state.get("is_legal_query", True):
#             logger.info("⚠️ NON-LEGAL QUERY - Skipping refinement")
#             return {
#                 "refined_query": user_query,
#                 "query_refinement": {"refined_query": user_query},
#                 "step_times": {**state.get("step_times", {}), "query_refinement": 0.0}
#             }

#         # Refine query with context and synonyms
#         refinement_prompt = self.query_refinement_prompt.format(
#             query=user_query,
#             legal_domain="general"
#         )
#         refinement_response = self.llm.invoke([SystemMessage(content=refinement_prompt)])

#         try:
#             query_refinement = json.loads(refinement_response.content)
#             refined_query = query_refinement.get("refined_query", user_query)
#             logger.info(f"🔧 REFINED QUERY: {refined_query}")
#         except json.JSONDecodeError:
#             logger.warning("Failed to parse query refinement, using original")
#             refined_query = user_query
#             query_refinement = {"refined_query": user_query}

#         step_time = time.time() - step_start
#         step_times = state.get("step_times", {})
#         step_times["query_refinement"] = step_time

#         logger.info(f"✅ STEP 2 COMPLETE - Query refined in {step_time:.2f}s")

#         return {
#             "refined_query": refined_query,
#             "query_refinement": query_refinement,
#             "step_times": step_times
#         }

#     def query_analysis_node(self, state: AgentState):
#         """STEP 3: Analyze refined query and determine optimal strategy"""
#         step_start = time.time()
#         logger.info("🔄 STEP 3 START - Query analysis")

#         user_query = state.get("original_query", "")
#         refined_query = state.get("refined_query", user_query)

#         # Skip analysis if not legal query
#         if not state.get("is_legal_query", True):
#             logger.info("⚠️ NON-LEGAL QUERY - Returning non-legal response")
#             return {
#                 "query_analysis": {"is_legal": False},
#                 "step_times": {**state.get("step_times", {}), "query_analysis": 0.0}
#             }

#         logger.info(f"📝 ANALYZING REFINED QUERY: {refined_query}")
#         logger.info(f"🕐 STEP 3 TIMESTAMP: {get_current_timestamp()}")

#         # Analyze refined query to determine strategy
#         analysis_prompt = self.query_analysis_prompt.format(
#             query=user_query,
#             refined_query=refined_query
#         )
#         analysis_response = self.llm.invoke([SystemMessage(content=analysis_prompt)])

#         try:
#             # Parse JSON response
#             query_analysis = json.loads(analysis_response.content)
#             logger.info(f"� QUERY ANALYSIS: {query_analysis}")
#         except json.JSONDecodeError:
#             # Fallback to default analysis
#             logger.warning("Failed to parse query analysis, using defaults")
#             query_analysis = {
#                 "complexity": "moderate",
#                 "query_type": "mixed",
#                 "needs_precedents": True,
#                 "needs_statutes": True,
#                 "needs_summaries": True,
#                 "legal_domain": "other",
#                 "confidence": 0.5,
#                 "reasoning": "Default analysis due to parsing error"
#             }

#         step_time = time.time() - step_start
#         step_times = state.get("step_times", {})
#         step_times["query_analysis"] = step_time

#         logger.info(f"✅ STEP 3 COMPLETE - Query analyzed in {step_time:.2f}s")

#         return {
#             "query_analysis": query_analysis,
#             "step_times": step_times
#         }

#     def tool_selection_node(self, state: AgentState):
#         """STEP 4: Dynamically select tools based on query analysis"""
#         step_start = time.time()
#         logger.info("🔄 STEP 4 START - Dynamic tool selection")

#         # Skip tool selection if not legal query
#         if not state.get("is_legal_query", True):
#             logger.info("⚠️ NON-LEGAL QUERY - No tools needed")
#             return {
#                 "selected_tools": [],
#                 "step_times": {**state.get("step_times", {}), "tool_selection": 0.0}
#             }

#         query_analysis = state.get("query_analysis", {})
#         selected_tools = []

#         # Dynamic tool selection based on analysis
#         if query_analysis.get("needs_precedents", True):
#             selected_tools.append("najir_search")
#             logger.info("🏛️  SELECTED: najir_search (case law needed)")

#         if query_analysis.get("needs_statutes", True):
#             selected_tools.append("act_search")
#             logger.info("📜 SELECTED: act_search (statutes needed)")

#         if query_analysis.get("needs_summaries", True) or query_analysis.get("complexity") == "complex":
#             selected_tools.append("najir_summary")
#             logger.info("📋 SELECTED: najir_summary (summaries needed)")

#         # For simple queries, might only need one tool
#         if query_analysis.get("complexity") == "simple" and len(selected_tools) > 1:
#             # Prioritize based on query type
#             query_type = query_analysis.get("query_type", "mixed")
#             if query_type == "case_law":
#                 selected_tools = ["najir_search"]
#             elif query_type == "statute":
#                 selected_tools = ["act_search"]
#             logger.info(f"🎯 SIMPLIFIED: Using only {selected_tools} for simple query")

#         step_time = time.time() - step_start
#         step_times = state.get("step_times", {})
#         step_times["tool_selection"] = step_time

#         logger.info(f"✅ STEP 4 COMPLETE - Selected {len(selected_tools)} tools in {step_time:.2f}s")

#         return {
#             "selected_tools": selected_tools,
#             "step_times": step_times
#         }

#     def non_legal_response_node(self, state: AgentState):
#         """Handle non-legal queries with appropriate response"""
#         step_start = time.time()
#         logger.info("🔄 NON-LEGAL RESPONSE - Generating response for non-legal query")

#         user_query = state.get("original_query", "")

#         non_legal_response = f"""I apologize, but your query "{user_query}" doesn't appear to be related to legal matters.

# I am a specialized legal research assistant designed to help with:
# - Nepali legal cases and precedents
# - Legal statutes and regulations
# - Legal procedures and processes
# - Legal rights and obligations

# Please ask a legal question, and I'll be happy to help you with comprehensive legal research and analysis."""

#         step_time = time.time() - step_start
#         step_times = state.get("step_times", {})
#         step_times["non_legal_response"] = step_time

#         logger.info(f"✅ NON-LEGAL RESPONSE COMPLETE - Generated in {step_time:.2f}s")

#         return {
#             "final_response": non_legal_response,
#             "step_times": step_times
#         }

#     async def dynamic_tools_node(self, state: AgentState):
#         """STEP 5: Execute dynamically selected tools with refined queries"""
#         step_start = time.time()
#         logger.info("🔄 STEP 5 START - Dynamic tools execution")
#         logger.info(f"🕐 STEP 5 TIMESTAMP: {get_current_timestamp()}")

#         selected_tools = state.get("selected_tools", [])
#         user_query = state.get("original_query", "")
#         refined_query = state.get("refined_query", user_query)
#         query_refinement = state.get("query_refinement", {})
#         query_analysis = state.get("query_analysis", {})
#         complexity = query_analysis.get("complexity", "moderate")

#         # Use refined query for tool execution
#         execution_query = refined_query if refined_query else user_query
#         logger.info(f"🔧 EXECUTING WITH REFINED QUERY: {execution_query}")

#         if not selected_tools:
#             logger.warning("⚠️  NO TOOLS SELECTED - Using default tools")
#             selected_tools = ["najir_search", "act_search", "najir_summary"]

#         # Setup parallel execution for selected tools only
#         tasks = []
#         tool_map = {}
#         tools_used = []

#         for tool_name in selected_tools:
#             tools_used.append(tool_name)
#             logger.info(f"🔧 PREPARING TOOL - {tool_name} (complexity: {complexity})")

#             for tool in self.tools:
#                 if tool.name == tool_name:
#                     task = tool.arun({"query": execution_query, "complexity": complexity})
#                     tasks.append(task)
#                     tool_map[len(tasks) - 1] = tool_name
#                     break

#         logger.info(f"🚀 EXECUTING DYNAMIC - {len(tasks)} selected tools running")
#         execution_start = time.time()

#         # Execute selected tools in parallel
#         results = await asyncio.gather(*tasks, return_exceptions=True)

#         execution_time = time.time() - execution_start
#         logger.info(f"⚡ DYNAMIC EXECUTION COMPLETE - {execution_time:.2f}s")

#         # Process results
#         najir_response = act_response = najir_summary_response = ""
#         najir_sources = act_sources = najir_summary_sources = []

#         for i, result in enumerate(results):
#             if isinstance(result, Exception):
#                 logger.error(f"❌ TOOL ERROR - Index {i}: {result}")
#                 continue

#             try:
#                 parsed_result = json.loads(result)
#                 tool_name = tool_map[i]

#                 if tool_name == "najir_search":
#                     najir_response = parsed_result.get('response', '')
#                     najir_sources = parsed_result.get('sources', [])
#                     logger.info(f"📊 NAJIR RESULTS - Response length: {len(najir_response)}, Sources: {len(najir_sources)}")
#                 elif tool_name == "act_search":
#                     act_response = parsed_result.get('response', '')
#                     act_sources = parsed_result.get('sources', [])
#                     logger.info(f"📊 ACT RESULTS - Response length: {len(act_response)}, Sources: {len(act_sources)}")
#                 elif tool_name == "najir_summary":
#                     najir_summary_response = parsed_result.get('response', '')
#                     najir_summary_sources = parsed_result.get('sources', [])
#                     logger.info(f"📊 SUMMARY RESULTS - Response length: {len(najir_summary_response)}, Sources: {len(najir_summary_sources)}")

#             except Exception as e:
#                 logger.error(f"❌ RESULT PROCESSING ERROR - {e}")
        
#         step_time = time.time() - step_start
#         logger.info(f"✅ STEP 5 COMPLETE - All tools processed in {step_time:.2f}s")

#         # Update step times and tools used
#         step_times = state.get("step_times", {})
#         step_times["dynamic_tools"] = step_time

#         return {
#             "tools_used": tools_used,
#             "najir_response": najir_response,
#             "act_response": act_response,
#             "najir_summary_response": najir_summary_response,
#             "najir_sources": najir_sources,
#             "act_sources": act_sources,
#             "najir_summary_sources": najir_summary_sources,
#             "step_times": step_times
#         }

#     def quality_assessment_node(self, state: AgentState):
#         """STEP 6: Assess quality of retrieved results"""
#         step_start = time.time()
#         logger.info("🔄 STEP 6 START - Quality assessment")

#         # Calculate quality score based on multiple factors
#         najir_sources = state.get("najir_sources", [])
#         act_sources = state.get("act_sources", [])
#         summary_sources = state.get("najir_summary_sources", [])

#         total_sources = len(najir_sources) + len(act_sources) + len(summary_sources)

#         # Base quality score
#         quality_score = 0.3

#         # Source count factor (more sources = higher quality)
#         if total_sources >= 5:
#             quality_score += 0.3
#         elif total_sources >= 3:
#             quality_score += 0.2
#         elif total_sources >= 1:
#             quality_score += 0.1

#         # Source diversity factor
#         source_types = sum([
#             1 if najir_sources else 0,
#             1 if act_sources else 0,
#             1 if summary_sources else 0
#         ])
#         quality_score += source_types * 0.1

#         # Response quality factor (check if responses are meaningful)
#         responses = [
#             state.get("najir_response", ""),
#             state.get("act_response", ""),
#             state.get("najir_summary_response", "")
#         ]
#         meaningful_responses = sum(1 for r in responses if len(r.strip()) > 50)
#         quality_score += meaningful_responses * 0.05

#         quality_score = min(1.0, quality_score)

#         # Determine if refinement is needed
#         needs_refinement = quality_score < 0.6 and state.get("iteration_count", 0) < 2

#         step_time = time.time() - step_start
#         step_times = state.get("step_times", {})
#         step_times["quality_assessment"] = step_time

#         logger.info(f"📊 QUALITY SCORE: {quality_score:.2f}")
#         logger.info(f"🔄 NEEDS REFINEMENT: {needs_refinement}")
#         logger.info(f"✅ STEP 6 COMPLETE - Quality assessed in {step_time:.2f}s")

#         return {
#             "quality_score": quality_score,
#             "needs_refinement": needs_refinement,
#             "step_times": step_times
#         }

#     def synthesis_node(self, state: AgentState):
#         """STEP 7: Intelligently synthesize responses based on quality and available data"""
#         step_start = time.time()
#         logger.info("🔄 STEP 7 START - Intelligent response synthesis")
#         logger.info(f"🕐 STEP 7 TIMESTAMP: {get_current_timestamp()}")

#         user_query = state.get("original_query", "")
#         quality_score = state.get("quality_score", 0.5)
#         query_analysis = state.get("query_analysis", {})

#         # Adaptive synthesis based on available responses
#         available_responses = []
#         if state.get("najir_response"):
#             available_responses.append(f"Case Law Analysis: {state.get('najir_response')}")
#         if state.get("act_response"):
#             available_responses.append(f"Statutory Analysis: {state.get('act_response')}")
#         if state.get("najir_summary_response"):
#             available_responses.append(f"Legal Summary: {state.get('najir_summary_response')}")

#         # Enhanced synthesis prompt based on query complexity
#         complexity = query_analysis.get("complexity", "moderate")
#         legal_domain = query_analysis.get("legal_domain", "general")

#         enhanced_synthesis_prompt = f"""
# You are a legal expert providing comprehensive analysis for Nepali law.

# Original Query: {user_query}
# Query Complexity: {complexity}
# Legal Domain: {legal_domain}
# Quality Score: {quality_score:.2f}

# Available Legal Sources:
# {chr(10).join(available_responses) if available_responses else "Limited sources available"}

# Instructions:
# 1. Provide a direct, comprehensive answer to the user's query
# 2. Structure your response clearly with proper legal reasoning
# 3. Reference specific legal provisions or precedents when available
# 4. If quality is low ({quality_score:.2f}), acknowledge limitations
# 5. Use proper Nepali legal terminology where appropriate
# 6. Respond in the same language as the user's query

# Provide a well-structured legal analysis that addresses the query comprehensively.
# """

#         logger.info(f"🎯 SYNTHESIZING RESPONSE - Complexity: {complexity}, Quality: {quality_score:.2f}")

#         synthesis_start = time.time()
#         final_response = self.llm.invoke([SystemMessage(content=enhanced_synthesis_prompt)])
#         synthesis_time = time.time() - synthesis_start

#         step_time = time.time() - step_start

#         logger.info(f"🎯 SYNTHESIS COMPLETE - Generated in {synthesis_time:.2f}s")
#         logger.info(f"📝 FINAL RESPONSE LENGTH - {len(final_response.content)} characters")
#         logger.info(f"✅ STEP 7 COMPLETE - Final response ready in {step_time:.2f}s")
        
#         # Update step times
#         step_times = state.get("step_times", {})
#         step_times["synthesis"] = step_time
        
#         # Create final message with metadata
#         final_message = AIMessage(
#             content=final_response.content,
#             additional_kwargs={
#                 "role": "assistant",
#                 "sender": "assistant",
#                 "created_at": get_current_timestamp(),
#                 "message_type": "final_response",
#                 "tools_used": state.get("tools_used", []),
#                 "step_times": step_times,
#                 "synthesis_time": synthesis_time,
#                 "total_sources": {
#                     "najir": len(state.get("najir_sources", [])),
#                     "act": len(state.get("act_sources", [])),
#                     "summary": len(state.get("najir_summary_sources", []))
#                 }
#             }
#         )
        
#         return {
#             "messages": [final_message],
#             "final_response": final_response.content,
#             "step_times": step_times
#         }

#     def citation_refinement_node(self, state: AgentState):
#         """STEP 8: Add proper citations and refine the final response"""
#         step_start = time.time()
#         logger.info("🔄 STEP 8 START - Citation refinement")
#         logger.info(f"🕐 STEP 8 TIMESTAMP: {get_current_timestamp()}")

#         final_response = state.get("final_response", "")
#         user_query = state.get("original_query", "")

#         # Collect all sources without manipulation - simple numbered citations
#         all_sources = []
#         citation_counter = 1

#         # Process najir sources
#         najir_sources = state.get("najir_sources", [])
#         for source in najir_sources:
#             metadata = source.get("metadata", {})
#             all_sources.append({
#                 "citation_number": citation_counter,
#                 "type": "najir",
#                 "article_id": metadata.get("article_id", ""),
#                 "url": metadata.get("url", ""),
#                 "text": source.get("text", ""),
#                 "metadata": metadata,
#                 "score": source.get("score", 0.0)
#             })
#             citation_counter += 1

#         # Process act sources
#         act_sources = state.get("act_sources", [])
#         for source in act_sources:
#             metadata = source.get("metadata", {})
#             all_sources.append({
#                 "citation_number": citation_counter,
#                 "type": "act",
#                 "title": metadata.get("title", ""),
#                 "pdf": metadata.get("pdf", metadata.get("pdf_link", "")),
#                 "text": source.get("text", ""),
#                 "metadata": metadata,
#                 "score": source.get("score", 0.0)
#             })
#             citation_counter += 1

#         # Process summary sources
#         summary_sources = state.get("najir_summary_sources", [])
#         for source in summary_sources:
#             metadata = source.get("metadata", {})
#             all_sources.append({
#                 "citation_number": citation_counter,
#                 "type": "najir_summary",
#                 "article_id": metadata.get("article_id", ""),
#                 "url": metadata.get("url", ""),
#                 "text": source.get("text", ""),
#                 "metadata": metadata,
#                 "score": source.get("score", 0.0)
#             })
#             citation_counter += 1

#         # Create simple sources list for citation
#         sources_text = ""
#         for source in all_sources:
#             sources_text += f"[{source['citation_number']}] Type: {source['type']}\n"

#             if source['type'] == 'act':
#                 sources_text += f"Title: {source['title']}\n"
#                 sources_text += f"PDF: {source['pdf']}\n"
#             elif source['type'] in ['najir', 'najir_summary']:
#                 sources_text += f"Article ID: {source['article_id']}\n"
#                 sources_text += f"URL: {source['url']}\n"

#             sources_text += f"Content: {source['text'][:300]}...\n"
#             sources_text += f"Score: {source['score']:.2f}\n\n"

#         citation_prompt = f"""
# You are a legal expert. Add simple numbered citations to the response.

# Original Query: {user_query}

# Current Response:
# {final_response}

# Available Sources:
# {sources_text}

# Instructions:
# 1. Add citations using [1], [2], [3] format throughout the response
# 2. Only cite sources that are actually relevant to the claims made
# 3. Maintain the same language as the original query
# 4. Keep the response clear and professional

# Provide the response with numbered citations [1], [2], [3]:
# """

#         logger.info(f"🔗 ADDING CITATIONS - Processing {len(all_sources)} sources")

#         citation_start = time.time()
#         cited_response = self.llm.invoke([SystemMessage(content=citation_prompt)])
#         citation_time = time.time() - citation_start

#         step_time = time.time() - step_start

#         # Create simple citation mapping
#         citation_mapping = {}
#         for source in all_sources:
#             citation_num = source["citation_number"]

#             if source["type"] == "act":
#                 citation_mapping[citation_num] = {
#                     "type": "act",
#                     "title": source["title"],
#                     "pdf": source["pdf"]
#                 }
#             elif source["type"] == "najir":
#                 citation_mapping[citation_num] = {
#                     "type": "najir",
#                     "article_id": source["article_id"],
#                     "url": source["url"]
#                 }
#             elif source["type"] == "najir_summary":
#                 citation_mapping[citation_num] = {
#                     "type": "najir_summary",
#                     "article_id": source["article_id"],
#                     "url": source["url"]
#                 }

#         citation_metadata = {
#             "total_sources": len(all_sources),
#             "citation_mapping": citation_mapping,
#             "citation_time": citation_time
#         }

#         step_times = state.get("step_times", {})
#         step_times["citation_refinement"] = step_time

#         logger.info(f"🔗 CITATION COMPLETE - Added citations in {citation_time:.2f}s")
#         logger.info(f"📚 SOURCES CITED - {len(all_sources)} sources processed")
#         logger.info(f"✅ STEP 6 COMPLETE - Citation refinement ready in {step_time:.2f}s")

#         return {
#             "cited_response": cited_response.content,
#             "citation_metadata": citation_metadata,
#             "step_times": step_times
#         }

#     def should_refine_query(self, state: AgentState) -> str:
#         """Conditional logic to determine if query needs refinement"""
#         needs_refinement = state.get("needs_refinement", False)
#         iteration_count = state.get("iteration_count", 0)

#         if needs_refinement and iteration_count < 2:
#             logger.info("🔄 ROUTING: Query needs refinement")
#             return "query_analysis"
#         else:
#             logger.info("✅ ROUTING: Proceeding to synthesis")
#             return "synthesis"

#     def should_add_citations(self, state: AgentState) -> str:
#         """Conditional logic to determine if citations should be added"""
#         # Always add citations as final step (state parameter required by LangGraph)
#         logger.info("🔗 ROUTING: Adding citations")
#         return "citation_refinement"

#     def build_graph(self):
#         """Build dynamic 6-step workflow graph with citation refinement"""
#         logger.info("🏗️  BUILDING WORKFLOW - Creating dynamic 6-step graph with citations")

#         builder = StateGraph(AgentState)

#         # Add all nodes
#         builder.add_node("query_analysis", self.query_analysis_node)
#         builder.add_node("tool_selection", self.tool_selection_node)
#         builder.add_node("dynamic_tools", self.dynamic_tools_node)
#         builder.add_node("quality_assessment", self.quality_assessment_node)
#         builder.add_node("synthesis", self.synthesis_node)
#         builder.add_node("citation_refinement", self.citation_refinement_node)

#         # Build the workflow
#         builder.add_edge(START, "query_analysis")
#         builder.add_edge("query_analysis", "tool_selection")
#         builder.add_edge("tool_selection", "dynamic_tools")
#         builder.add_edge("dynamic_tools", "quality_assessment")

#         # Conditional edge for refinement
#         builder.add_conditional_edges(
#             "quality_assessment",
#             self.should_refine_query,
#             {
#                 "query_analysis": "query_analysis",  # Refine if quality is low
#                 "synthesis": "synthesis"  # Proceed if quality is good
#             }
#         )

#         # Always add citations after synthesis
#         builder.add_conditional_edges(
#             "synthesis",
#             self.should_add_citations,
#             {
#                 "citation_refinement": "citation_refinement"
#             }
#         )

#         self.graph = builder.compile(checkpointer=self.checkpointer)

#         logger.info("✅ WORKFLOW BUILT - Dynamic 6-step graph ready with citation refinement")
#         return self.graph

#     async def run(self, query: str, thread_id: Optional[str] = None):
#         """Execute 3-step workflow with MongoDB persistence and proper timestamps"""
#         workflow_start = time.time()
#         user_id = str(self.current_user.user.id)
        
#         logger.info("🚀 WORKFLOW START - Beginning legal research")
#         logger.info(f"🕐 WORKFLOW TIMESTAMP: {get_current_timestamp()}")
#         logger.info(f"👤 USER: {user_id}")
#         logger.info(f"🔗 THREAD: {thread_id or user_id}")
#         logger.info(f"❓ QUERY: {query}")
        
#         if not self.tools:
#             self.setup_tools()
#         if not self.graph:
#             self.build_graph()
        
#         config = {"configurable": {"thread_id": thread_id or user_id,"recursion_limit": 50}}
        
#         initial_state = {
#             "messages": [HumanMessage(
#                 content=query,
#                 additional_kwargs={
#                     "role": "user",
#                     "sender": "user",
#                     "created_at": get_current_timestamp(),
#                     "user_id": user_id,
#                     "message_type": "query"
#                 }
#             )],
#             "original_query": query,
#             "query_analysis": {},
#             "tools_used": [],
#             "selected_tools": [],
#             "najir_response": "",
#             "act_response": "",
#             "najir_summary_response": "",
#             "najir_sources": [],
#             "act_sources": [],
#             "najir_summary_sources": [],
#             "final_response": "",
#             "start_time": workflow_start,
#             "step_times": {},
#             "quality_score": 0.0,
#             "needs_refinement": False,
#             "iteration_count": 0,
#             "cited_response": "",
#             "citation_metadata": {}
#         }
        
#         # Execute workflow
#         result = await self.graph.ainvoke(initial_state, config=config)
        
#         workflow_time = time.time() - workflow_start
        
#         # Save to chat history with all metadata
#         final_response = result.get("final_response", "")
#         tools_used = result.get("tools_used", [])
#         step_times = result.get("step_times", {})
#         step_times["total_workflow"] = workflow_time
        
#         await self.chat_manager.save_interaction(
#             user_id=user_id, 
#             query=query, 
#             response=final_response,
#             tools_used=tools_used,
#             step_times=step_times
#         )
        
#         logger.info(f"🏁 WORKFLOW COMPLETE - Total time: {workflow_time:.2f}s")
#         logger.info(f"🕐 WORKFLOW END TIMESTAMP: {get_current_timestamp()}")
#         logger.info(f"🛠️  TOOLS USED: {', '.join(tools_used)}")
        
#         return result

# @router.post("/agent")
# async def legal_agent_chat(
#     query: str = Query(..., description="The legal query to process"),
#     thread_id: Optional[str] = Query(None, description="Optional thread ID for conversation continuity"),
#     current_user: CurrentUser = Depends(get_tenant_info)
# ):
#     """
#     Legal research agent with MongoDB persistence and proper message structure
    
#     - **query**: The legal question or query in Nepali or English
#     - **thread_id**: Optional thread ID for maintaining conversation context
#     """
#     request_start = time.time()
#     request_timestamp = get_current_timestamp()
    
#     logger.info("📡 API REQUEST START")
#     logger.info(f"🕐 REQUEST TIMESTAMP: {request_timestamp}")
#     logger.info(f"👤 USER: {current_user.user.id}")
#     logger.info(f"❓ QUERY: {query}")
#     logger.info(f"🔗 THREAD: {thread_id}")
    
#     try:
#         # Decode URL-encoded query if needed
#         decoded_query = urllib.parse.unquote(query)
#         logger.info(f"🔤 DECODED QUERY: {decoded_query}")
        
#         agent = LegalAgent(current_user)
#         result = await agent.run(decoded_query, thread_id)
        
#         request_time = time.time() - request_start
        
#         # Process najir sources
#         najir_sources: Dict[str, Any] = await _process_sources(
#             result.get("najir_sources", []), 
#             current_user
#         )
        
#         # Ultra-minimal response - only essential fields
#         response_data = {
#             "final_response": result.get("cited_response", result.get("final_response", "")),
#             "cited_response": result.get("cited_response", result.get("final_response", "")),
#             "original_response": result.get("final_response", ""),
#             "najir_response": result.get("najir_response", ""),
#             "act_response": result.get("act_response", ""),
#             "najir_summary_response": result.get("najir_summary_response", ""),
#             "najir_sources": najir_sources.get("sources", [])
#         }
        
#         logger.info(f"✅ API SUCCESS - Request completed in {request_time:.2f}s")
#         logger.info(f"🕐 RESPONSE TIMESTAMP: {get_current_timestamp()}")
        
#         return response_data
        
#     except Exception as e:
#         request_time = time.time() - request_start
#         error_timestamp = get_current_timestamp()
        
#         logger.error(f"❌ API ERROR - Failed after {request_time:.2f}s: {e}")
#         logger.error(f"🕐 ERROR TIMESTAMP: {error_timestamp}")
        
#         # Streamlined error response
#         return {
#             "final_response": f"Error processing your request: {str(e)}",
#             "cited_response": "",
#             "original_response": "",
#             "najir_response": "",
#             "act_response": "",
#             "najir_summary_response": "",
#             "najir_sources": []
#         }