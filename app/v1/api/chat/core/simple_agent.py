from app.core.security import get_tenant_info
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from app.v1.api.chat.core.tools import LegalTools
from app.v1.api.chat.core.utils.process_najirs import _process_sources

from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, BaseMessage, SystemMessage, AIMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.checkpoint.mongodb import AsyncMongoDBSaver
from fastapi import APIRouter, Depends, Query
from typing import Annotated, List, Optional, Dict, Any
import json
import asyncio
import time
from datetime import datetime, timezone
import urllib.parse

logger = StructuredLogger(__name__)
router = APIRouter()

class SimpleAgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    original_query: str
    is_legal_query: bool
    refined_query: str
    najir_response: str
    act_response: str
    najir_summary_response: str
    najir_sources: List[dict]
    act_sources: List[dict]
    najir_summary_sources: List[dict]
    final_response: str
    citation_mapping: Dict[str, Any]
    step_times: Dict[str, float]

def get_current_timestamp():
    """Get current UTC timestamp in ISO format"""
    return datetime.now(timezone.utc).isoformat()

class SimpleLegalAgent:
    def __init__(self, current_user: CurrentUser):
        logger.info("🤖 SIMPLE AGENT INIT - Starting SimpleLegalAgent")
        
        self.current_user = current_user
        self.llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        self.tools = []
        self.checkpointer = AsyncMongoDBSaver(
            client=current_user.db.async_client,
            db_name=current_user.db.read_db.name,
            collection_name="checkpoints"
        )
        self.graph = None
        
        self.query_analysis_prompt = """
Analyze this query to determine if it's legal-related and what tools are needed:

Query: {query}

Respond with JSON:
{{
    "is_legal": true/false,
    "refined_query": "enhanced query with legal context and synonyms",
    "needs_najir": true/false,
    "needs_act": true/false, 
    "needs_summary": true/false,
    "reasoning": "explanation"
}}

Legal indicators:
- Nepali: कानून, अदालत, मुद्दा, फैसला, ऐन, धारा, ठगी, चोरी, हत्या, जग्गा, सम्पत्ति
- English: law, court, case, judgment, act, section, legal, crime, theft, fraud, property

Query refinement examples:
- "ठगी" → "ठगी धोका fraud cheating दण्ड संहिता"
- "जग्गा विवाद" → "जग्गा मुद्दा सम्पत्ति विवाद भूमि ऐन property dispute"
"""

        logger.info("✅ SIMPLE AGENT INIT COMPLETE")

    def setup_tools(self):
        """Setup tools"""
        user_id = str(self.current_user.user.id)
        logger.info(f"🔨 CREATING TOOLS - For user: {user_id}")
        legal_tools = LegalTools()
        self.tools = legal_tools.create_tools(self.current_user)

    def step1_query_analysis(self, state: SimpleAgentState):
        """STEP 1: Analyze query and determine if tools are needed"""
        step_start = time.time()
        logger.info("🔄 STEP 1 START - Query analysis and refinement")

        user_query = state.get("original_query", state["messages"][-1].content)
        logger.info(f"📝 USER QUERY: {user_query}")

        # Analyze query to determine if legal and what tools needed
        analysis_prompt = self.query_analysis_prompt.format(query=user_query)
        analysis_response = self.llm.invoke([SystemMessage(content=analysis_prompt)])

        try:
            analysis_data = json.loads(analysis_response.content)
            is_legal = analysis_data.get("is_legal", False)
            refined_query = analysis_data.get("refined_query", user_query)
            needs_najir = analysis_data.get("needs_najir", False)
            needs_act = analysis_data.get("needs_act", False)
            needs_summary = analysis_data.get("needs_summary", False)
            
            logger.info(f"🏛️ IS LEGAL: {is_legal}")
            logger.info(f"🔧 REFINED QUERY: {refined_query}")
            logger.info(f"🛠️ TOOLS NEEDED: najir={needs_najir}, act={needs_act}, summary={needs_summary}")
        except json.JSONDecodeError:
            logger.warning("Failed to parse analysis, assuming legal query")
            is_legal = True
            refined_query = user_query
            needs_najir = needs_act = needs_summary = True

        step_time = time.time() - step_start
        step_times = {"step1_analysis": step_time}

        logger.info(f"✅ STEP 1 COMPLETE - Analysis done in {step_time:.2f}s")

        return {
            "is_legal_query": is_legal,
            "refined_query": refined_query,
            "needs_najir": needs_najir,
            "needs_act": needs_act,
            "needs_summary": needs_summary,
            "step_times": step_times
        }

    async def step2_parallel_tools(self, state: SimpleAgentState):
        """STEP 2: Execute tools in parallel if needed"""
        step_start = time.time()
        logger.info("🔄 STEP 2 START - Parallel tool execution")

        # If not legal query, skip tools
        if not state.get("is_legal_query", True):
            logger.info("⚠️ NON-LEGAL QUERY - Skipping tools")
            step_time = time.time() - step_start
            return {
                "najir_response": "",
                "act_response": "",
                "najir_summary_response": "",
                "najir_sources": [],
                "act_sources": [],
                "najir_summary_sources": [],
                "step_times": {**state.get("step_times", {}), "step2_tools": step_time}
            }

        refined_query = state.get("refined_query", state.get("original_query", ""))
        
        # Determine which tools to run
        tasks = []
        tool_map = {}
        
        if state.get("needs_najir", False):
            for tool in self.tools:
                if tool.name == "najir_search":
                    tasks.append(tool.arun({"query": refined_query}))
                    tool_map[len(tasks) - 1] = "najir_search"
                    break
        
        if state.get("needs_act", False):
            for tool in self.tools:
                if tool.name == "act_search":
                    tasks.append(tool.arun({"query": refined_query}))
                    tool_map[len(tasks) - 1] = "act_search"
                    break
        
        if state.get("needs_summary", False):
            for tool in self.tools:
                if tool.name == "najir_summary":
                    tasks.append(tool.arun({"query": refined_query}))
                    tool_map[len(tasks) - 1] = "najir_summary"
                    break

        logger.info(f"🚀 EXECUTING {len(tasks)} tools in parallel")
        
        # Execute tools in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True) if tasks else []
        
        # Process results
        najir_response = act_response = najir_summary_response = ""
        najir_sources = act_sources = najir_summary_sources = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ TOOL ERROR - Index {i}: {result}")
                continue

            try:
                parsed_result = json.loads(result)
                tool_name = tool_map[i]

                if tool_name == "najir_search":
                    najir_response = parsed_result.get('response', '')
                    najir_sources = parsed_result.get('sources', [])
                elif tool_name == "act_search":
                    act_response = parsed_result.get('response', '')
                    act_sources = parsed_result.get('sources', [])
                elif tool_name == "najir_summary":
                    najir_summary_response = parsed_result.get('response', '')
                    najir_summary_sources = parsed_result.get('sources', [])

            except Exception as e:
                logger.error(f"❌ RESULT PROCESSING ERROR - {e}")

        step_time = time.time() - step_start
        logger.info(f"✅ STEP 2 COMPLETE - Tools executed in {step_time:.2f}s")

        return {
            "najir_response": najir_response,
            "act_response": act_response,
            "najir_summary_response": najir_summary_response,
            "najir_sources": najir_sources,
            "act_sources": act_sources,
            "najir_summary_sources": najir_summary_sources,
            "step_times": {**state.get("step_times", {}), "step2_tools": step_time}
        }

    def step3_final_response(self, state: SimpleAgentState):
        """STEP 3: Generate final response with citations"""
        step_start = time.time()
        logger.info("🔄 STEP 3 START - Final response with citations")

        user_query = state.get("original_query", "")
        
        # Handle non-legal queries
        if not state.get("is_legal_query", True):
            non_legal_response = f"""I apologize, but your query "{user_query}" doesn't appear to be related to legal matters. 

I am a specialized legal research assistant designed to help with:
- Nepali legal cases and precedents
- Legal statutes and regulations  
- Legal procedures and processes
- Legal rights and obligations

Please ask a legal question, and I'll be happy to help you with comprehensive legal research and analysis."""
            
            step_time = time.time() - step_start
            return {
                "final_response": non_legal_response,
                "citation_mapping": {},
                "step_times": {**state.get("step_times", {}), "step3_final": step_time}
            }

        # Synthesize legal response
        najir_response = state.get("najir_response", "")
        act_response = state.get("act_response", "")
        najir_summary_response = state.get("najir_summary_response", "")

        synthesis_prompt = f"""
Based on the legal research results, provide a comprehensive answer to the user's query.

User Query: {user_query}

Legal Research Results:
- Case Law: {najir_response}
- Statutes: {act_response}  
- Legal Summaries: {najir_summary_response}

Provide a well-structured legal analysis that directly answers the query.
Use [1], [2], [3] format for citations.
Respond in the same language as the user's query.
"""

        final_response = self.llm.invoke([SystemMessage(content=synthesis_prompt)])
        
        # Create simple citation mapping
        citation_mapping = {}
        citation_counter = 1
        
        for source in state.get("najir_sources", []):
            citation_mapping[str(citation_counter)] = {
                "type": "najir",
                "metadata": source.get("metadata", {})
            }
            citation_counter += 1

        step_time = time.time() - step_start
        logger.info(f"✅ STEP 3 COMPLETE - Final response ready in {step_time:.2f}s")

        return {
            "final_response": final_response.content,
            "citation_mapping": citation_mapping,
            "step_times": {**state.get("step_times", {}), "step3_final": step_time}
        }

    def should_run_tools(self, state: SimpleAgentState) -> str:
        """Route to tools if legal query, otherwise skip to final response"""
        if state.get("is_legal_query", True):
            logger.info("✅ ROUTING: Running tools for legal query")
            return "step2_tools"
        else:
            logger.info("⚠️ ROUTING: Skipping tools for non-legal query")
            return "step3_final"

    def build_graph(self):
        """Build simple 3-step workflow graph"""
        logger.info("🏗️ BUILDING SIMPLE WORKFLOW - 3 steps only")

        builder = StateGraph(SimpleAgentState)

        # Add 3 nodes
        builder.add_node("step1_analysis", self.step1_query_analysis)
        builder.add_node("step2_tools", self.step2_parallel_tools)
        builder.add_node("step3_final", self.step3_final_response)

        # Build the workflow
        builder.add_edge(START, "step1_analysis")
        
        # Conditional routing after analysis
        builder.add_conditional_edges(
            "step1_analysis",
            self.should_run_tools,
            {
                "step2_tools": "step2_tools",
                "step3_final": "step3_final"
            }
        )
        
        # Always go to final response after tools
        builder.add_edge("step2_tools", "step3_final")

        self.graph = builder.compile(checkpointer=self.checkpointer)
        logger.info("✅ SIMPLE WORKFLOW BUILT - 3 steps ready")
        return self.graph

    async def run(self, query: str, thread_id: Optional[str] = None):
        """Execute simple 3-step workflow"""
        workflow_start = time.time()
        user_id = str(self.current_user.user.id)
        
        logger.info("🚀 SIMPLE WORKFLOW START")
        logger.info(f"👤 USER: {user_id}")
        logger.info(f"❓ QUERY: {query}")
        
        if not self.tools:
            self.setup_tools()
        if not self.graph:
            self.build_graph()
        
        config = {"configurable": {"thread_id": thread_id or user_id}}
        
        initial_state = {
            "messages": [HumanMessage(content=query)],
            "original_query": query,
            "is_legal_query": True,
            "refined_query": "",
            "najir_response": "",
            "act_response": "",
            "najir_summary_response": "",
            "najir_sources": [],
            "act_sources": [],
            "najir_summary_sources": [],
            "final_response": "",
            "citation_mapping": {},
            "step_times": {}
        }
        
        # Execute workflow
        result = await self.graph.ainvoke(initial_state, config=config)
        
        workflow_time = time.time() - workflow_start
        logger.info(f"🏁 SIMPLE WORKFLOW COMPLETE - Total time: {workflow_time:.2f}s")
        
        return result
