from langchain_core.tools import StructuredTool
from llama_index.core.query_engine import Citation<PERSON><PERSON>yEngine,RetrieverQueryEngine
from llama_index.core.postprocessor import LLMRerank
from llama_index.core import QueryBundle
from llama_index.llms.openai import OpenAI
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
import json
import time

logger = StructuredLogger(__name__)

class LegalTools:
    def __init__(self):
        logger.info("🔧 TOOLS INIT - Setting up Enhanced LegalTools")
        llm_index = OpenAI(model="gpt-4o-mini", temperature=0)
        self.rerank = LLMRerank(llm=llm_index, top_n=5)
        self.adaptive_rerank = LLMRerank(llm=llm_index, top_n=8)  # For complex queries
        logger.info("✅ RERANK SETUP - Configured with adaptive ranking")
    
    def create_tools(self, current_user: CurrentUser):
        """Create enhanced legal research tools with adaptive behavior"""
        logger.info(f"🛠️  CREATING ENHANCED TOOLS - For user: {current_user.user.id}")

        async def najir_search(query: str, complexity: str = "moderate") -> str:
            """Search case law and precedents with adaptive behavior"""
            start_time = time.time()
            logger.info("🏛️  NAJIR SEARCH START")
            logger.info(f"📝 QUERY: {query} (Complexity: {complexity})")

            try:
                # Adaptive parameters based on complexity
                if complexity == "simple":
                    similarity_top_k = 5
                    reranker = self.rerank
                elif complexity == "complex":
                    similarity_top_k = 12
                    reranker = self.adaptive_rerank
                else:  # moderate
                    similarity_top_k = 8
                    reranker = self.rerank

                logger.info("⚙️  NAJIR STEP 1/4 - Setting up adaptive citation engine")
                # citation_engine = CitationQueryEngine.from_args(
                #     index=current_user.qdrant.qd_index,
                #     similarity_top_k=similarity_top_k,
                #     citation_chunk_size=512,
                #     llm=current_user.llm.gemini,
                # )

                citation_engine = RetrieverQueryEngine.from_args(
                    retriever=current_user.qdrant.qd_index.as_retriever(),
                    similarity_top_k=similarity_top_k,
                    citation_chunk_size=512,
                    llm=current_user.llm.gemini,
                )
                logger.info("🔍 NAJIR STEP 2/4 - Executing query")
                result = await citation_engine.aquery(query)
                logger.info(f"📊 NAJIR STEP 2/4 - Retrieved {len(result.source_nodes)} nodes")

                logger.info("🎯 NAJIR STEP 3/4 - Adaptive reranking nodes")
                filtered_nodes = await reranker.apostprocess_nodes(
                    nodes=result.source_nodes,
                    query_bundle=QueryBundle(query_str=query)
                )
                
                logger.info("📝 NAJIR STEP 4/4 - Formatting sources")
                sources = [{
                    "text": node.text,
                    "score": getattr(node, 'score', 0.0),
                    "metadata": {
                        **getattr(node, 'metadata', {}),
                        'source_type': 'najir',
                        'search_query': query,
                        'retrieval_score': getattr(node, 'score', 0.0)
                    }
                } for node in filtered_nodes]
                
                duration = time.time() - start_time
                logger.info(f"✅ NAJIR SEARCH COMPLETE - {len(sources)} sources in {duration:.2f}s")
                
                return json.dumps({
                    "response": result.response,
                    "sources": sources
                })
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"❌ NAJIR SEARCH FAILED - {duration:.2f}s - Error: {e}")
                return json.dumps({"response": f"Error: {str(e)}", "sources": []})
        
        async def act_search(query: str, complexity: str = "moderate") -> str:
            """Search statutes and laws with adaptive behavior"""
            start_time = time.time()
            logger.info("📜 ACT SEARCH START")
            logger.info(f"📝 QUERY: {query} (Complexity: {complexity})")

            try:
                # Adaptive parameters based on complexity
                if complexity == "simple":
                    similarity_top_k = 5
                    reranker = self.rerank
                elif complexity == "complex":
                    similarity_top_k = 12
                    reranker = self.adaptive_rerank
                else:  # moderate
                    similarity_top_k = 8
                    reranker = self.rerank

                logger.info("⚙️  ACT STEP 1/4 - Setting up adaptive citation engine")
                # citation_engine = CitationQueryEngine.from_args(
                #     index=current_user.qdrant.act_index,
                #     similarity_top_k=similarity_top_k,
                #     citation_chunk_size=512,
                #     llm=current_user.llm.gemini,
                # )
                
                citation_engine = RetrieverQueryEngine.from_args(
                    retriever=current_user.qdrant.act_index.as_retriever(),
                    similarity_top_k=similarity_top_k,
                    citation_chunk_size=512,
                    llm=current_user.llm.gemini,
                )
                logger.info("🔍 ACT STEP 2/4 - Executing query")
                result = await citation_engine.aquery(query)
                logger.info(f"📊 ACT STEP 2/4 - Retrieved {len(result.source_nodes)} nodes")
                
                logger.info("🎯 ACT STEP 3/4 - Adaptive reranking nodes")
                filtered_nodes = await reranker.apostprocess_nodes(
                    nodes=result.source_nodes,
                    query_bundle=QueryBundle(query_str=query)
                )
                
                logger.info("📝 ACT STEP 4/4 - Formatting sources")
                sources = [{
                    "text": node.text,
                    "score": getattr(node, 'score', 0.0),
                    "metadata": {
                        **getattr(node, 'metadata', {}),
                        'source_type': 'act',
                        'search_query': query,
                        'retrieval_score': getattr(node, 'score', 0.0)
                    }
                } for node in filtered_nodes]
                
                duration = time.time() - start_time
                logger.info(f"✅ ACT SEARCH COMPLETE - {len(sources)} sources in {duration:.2f}s")
                
                return json.dumps({
                    "response": result.response,
                    "sources": sources
                })
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"❌ ACT SEARCH FAILED - {duration:.2f}s - Error: {e}")
                return json.dumps({"response": f"Error: {str(e)}", "sources": []})

        async def najir_summary(query: str, complexity: str = "moderate") -> str:
            """Get case law summaries with adaptive behavior"""
            start_time = time.time()
            logger.info("📋 NAJIR SUMMARY START")
            logger.info(f"📝 QUERY: {query} (Complexity: {complexity})")

            try:
                # Adaptive parameters for summaries (generally fewer needed)
                if complexity == "simple":
                    similarity_top_k = 3
                    reranker = self.rerank
                elif complexity == "complex":
                    similarity_top_k = 8
                    reranker = self.adaptive_rerank
                else:  # moderate
                    similarity_top_k = 5
                    reranker = self.rerank

                logger.info("⚙️  SUMMARY STEP 1/4 - Setting up adaptive citation engine")
                # citation_engine = CitationQueryEngine.from_args(
                #     index=current_user.qdrant.summary_index,
                #     similarity_top_k=similarity_top_k,
                #     citation_chunk_size=512,
                #     llm=current_user.llm.gemini
                # )
                citation_engine = RetrieverQueryEngine.from_args(
                    retriever=current_user.qdrant.summary_index.as_retriever(),
                    similarity_top_k=similarity_top_k,
                    citation_chunk_size=512,
                    llm=current_user.llm.gemini,
                )

                logger.info("🔍 SUMMARY STEP 2/4 - Executing query")
                result = await citation_engine.aquery(query)
                logger.info(f"📊 SUMMARY STEP 2/4 - Retrieved {len(result.source_nodes)} nodes")

                logger.info("🎯 SUMMARY STEP 3/4 - Adaptive reranking nodes")
                filtered_nodes = await reranker.apostprocess_nodes(
                    nodes=result.source_nodes,
                    query_bundle=QueryBundle(query_str=query)
                )
                
                logger.info("📝 SUMMARY STEP 4/4 - Formatting sources")
                sources = [{
                    "text": node.text,
                    "score": getattr(node, 'score', 0.0),
                    "metadata": {
                        **getattr(node, 'metadata', {}),
                        'source_type': 'najir_summary',
                        'search_query': query,
                        'retrieval_score': getattr(node, 'score', 0.0)
                    }
                } for node in filtered_nodes]
                
                duration = time.time() - start_time
                logger.info(f"✅ NAJIR SUMMARY COMPLETE - {len(sources)} sources in {duration:.2f}s")
                
                return json.dumps({
                    "response": result.response,
                    "sources": sources
                })
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"❌ NAJIR SUMMARY FAILED - {duration:.2f}s - Error: {e}")
                return json.dumps({"response": f"Error: {str(e)}", "sources": []})
        
        tools = [
            StructuredTool.from_function(
                najir_search,
                name="najir_search",
                description="Search Nepali case law and precedents with adaptive retrieval based on query complexity",
                coroutine=najir_search
            ),
            StructuredTool.from_function(
                act_search,
                name="act_search",
                description="Search Nepali statutes and laws with adaptive retrieval based on query complexity",
                coroutine=act_search
            ),
            StructuredTool.from_function(
                najir_summary,
                name="najir_summary",
                description="Get summaries of Nepali case law with adaptive retrieval for quick overviews",
                coroutine=najir_summary
            )
        ]
        
        logger.info(f"✅ TOOLS CREATED - {len(tools)} tools ready")
        return tools