from app.v1.api.knowledgebase.models import FilterOptions


async def get_filter_options_data(collection) -> FilterOptions:
    """Get filter options from database using single aggregation"""
    pipeline = [
        {
            "$group": {
                "_id": None,
                "court_types": {"$addToSet": "$court_type"},
                "mudda_types": {"$addToSet": "$mudda_type"},
                "months": {"$addToSet": "$month"},
                "article_ids": {"$addToSet": "$article_id"},
                "content_extracted_options": {"$addToSet": "$content_extracted"}
            }
        }
    ]
    
    result = await (await collection.aggregate(pipeline)).to_list(1)
    
    if result:
        data = result[0]
        return FilterOptions(
            court_types=[ct for ct in data.get("court_types", []) if ct],
            mudda_types=[mt for mt in data.get("mudda_types", []) if mt],
            months=[m for m in data.get("months", []) if m],
            article_ids=[aid for aid in data.get("article_ids", []) if aid][:100],
            content_extracted_options=[ce for ce in data.get("content_extracted_options", []) if ce is not None]
        )
    else:
        return FilterOptions(
            court_types=[],
            mudda_types=[],
            months=[],
            article_ids=[],
            content_extracted_options=[]
        )
