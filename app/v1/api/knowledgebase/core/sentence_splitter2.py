import re
from llama_index.core import Document
from llama_index.core.schema import TransformComponent
from llama_index.core.vector_stores import (
    MetadataFilter,
    MetadataFilters,
    FilterOperator, FilterCondition
)

from llama_index.llms.openai import OpenAI
from llama_index.core.schema import TextNode, TransformComponent
from typing import List, Set
from itertools import islice
from tqdm import tqdm
from llama_index.vector_stores.qdrant import QdrantVectorStore
import uuid
import asyncio

from nltk.tokenize import sent_tokenize
import uuid
from typing import List, Dict
from app.core.utils.resolve_llm import resolve_llm
from bson import ObjectId

def batch_iterator(iterable, batch_size):
    """Helper function to yield items in batches."""
    iterator = iter(iterable)
    while batch := list(islice(iterator, batch_size)):
        yield batch



# class SentenceSplitter_v2(TransformComponent):

#     def __init__(self, sentence_len=300, overlap_len=20):
#         super().__init__()
#         self._sentence_len = sentence_len
#         self._overlap_len = overlap_len

#     def __call__(self, documents, **kwargs):
#         print("**MetadataSimplify**")
#         nodes_ = []
#         for doc in documents:
#             doc_text = doc.text
#             grouped_sentences = self._split_stuffs(doc_text)
#             grouped_documents = [Document(text=sent['text'], metadata={**doc.metadata, 'sent_id': sent['id']}) for sent in grouped_sentences]
#             nodes_.extend(grouped_documents)
#         return nodes_

#     def _split_stuffs(self, text):
#         # Split the text into chunks of `self._sentence_len` characters with an overlap of `self._overlap_len`
#         chunk_size = self._sentence_len
#         overlap_size = self._overlap_len
#         chunks = []
        
#         start = 0
#         while start < len(text):
#             end = start + chunk_size
#             chunk = {"text": text[start:end], "id": str(uuid.uuid4())}  # Include chunk_id in each chunk
#             chunks.append(chunk)
#             start = end - overlap_size  # Set the next start to overlap
        
#         return chunks



class AddPageContext(TransformComponent):
    def __init__(self, page_vector_store: QdrantVectorStore, metadata_fields_to_search: Set[str], batch_size: int = 100,api_key_config=None):
        super().__init__()
        self._page_vector_store = page_vector_store
        self._metadata_fields_to_search = metadata_fields_to_search
        self._batch_size = batch_size
        
        self._llm = resolve_llm("models/gemini-2.0-flash", api_key_config)

    async def __call__(self, nodes: List[TextNode]) -> List[TextNode]:
        """
        Process nodes in batches to prevent memory overload.
        """
        transformed_nodes = []
        with tqdm(total=len(nodes), desc="Processing nodes") as pbar:
            for batch in batch_iterator(nodes, self._batch_size):
                tasks = [self._transform_node(node) for node in batch]
                results = await asyncio.gather(*tasks)
                transformed_nodes.extend(results)
                pbar.update(len(batch))
        return transformed_nodes

    async def _transform_node(self, node: TextNode) -> TextNode:
        """Fetch context and append it to the sentence."""
        return TextNode(text=await self._get_context(node) + node.text, metadata=node.metadata)

    async def _get_context(self, sentence_node: TextNode) -> str:
        """Use OpenAI to generate context for a sentence."""
        prompt = """
        <document>
        {doc_content}
        </document>
        <chunk>
        {chunk_content}
        </chunk>
        Provide a short context for the chunk.
        """
        page_node = self._get_page_node_from_sentence_node(sentence_node)
        response = await self._llm.acomplete(
            prompt=prompt.format(doc_content=page_node.text, chunk_content=sentence_node.text)
        )
        return response.text

    def _get_page_node_from_sentence_node(self, sentence_node: TextNode) -> TextNode:
        """Retrieve the corresponding page node using metadata filters."""
        metadata_filters = MetadataFilters(
            filters=[
                MetadataFilter(key=k, value=v, operator="==" if not isinstance(v, list) else "any")
                for k, v in sentence_node.metadata.items() if k in self._metadata_fields_to_search
            ],
            condition=FilterCondition.AND,
        )
        page_nodes = self._page_vector_store.get_nodes(filters=metadata_filters)
        return page_nodes[0] if page_nodes else None


class SentenceWithPageContext(TransformComponent):
    """
    Add context from full article document to sentence-split chunks.

    Takes sentence-split nodes and enriches them with context from the
    full article document stored in MongoDB.
    """

    def __init__(self, mongo_db, batch_size: int = 100, api_key_config=None):
        super().__init__()
        self._mongo_db = mongo_db  # MongoDB database instance
        self._batch_size = batch_size
        self._llm = resolve_llm("models/gemini-2.0-flash", api_key_config)

    async def __call__(self, nodes: List[TextNode]) -> List[TextNode]:
        """
        Process sentence nodes in batches and add context from full articles.
        """
        transformed_nodes = []
        with tqdm(total=len(nodes), desc="Adding context to sentence chunks") as pbar:
            for batch in batch_iterator(nodes, self._batch_size):
                tasks = [self._transform_sentence_node(node) for node in batch]
                results = await asyncio.gather(*tasks)
                transformed_nodes.extend(results)
                pbar.update(len(batch))
        return transformed_nodes

    async def _transform_sentence_node(self, sentence_node: TextNode) -> TextNode:
        """
        Transform a sentence node by adding context from the full article.
        """
        try:
            # Get the full article document from MongoDB
            article_doc = await self._get_article_from_mongo(sentence_node)

            if not article_doc:
                # If no article found, return original node
                return sentence_node

            # Generate context using LLM
            context = await self._generate_context(article_doc, sentence_node.text)

            # Create new node with context + original sentence
            enhanced_text = f"{context}\n\n{sentence_node.text}"

            # Add context metadata
            enhanced_metadata = {
                **sentence_node.metadata,
                "has_context": True,
                "context_length": len(context),
                "original_sentence_length": len(sentence_node.text)
            }

            return TextNode(
                text=enhanced_text,
                metadata=enhanced_metadata,
                node_id=sentence_node.node_id
            )

        except Exception as e:
            print(f"Error processing sentence node: {e}")
            # Return original node if error occurs
            return sentence_node

    async def _get_article_from_mongo(self, sentence_node: TextNode) -> dict:
        """
        Get the full article document from MongoDB using metadata.
        """
        try:
            # Look for article_mongo_id in metadata
            article_id = sentence_node.metadata.get('article_mongo_id')

            if not article_id:
                # Try other possible ID fields
                article_id = sentence_node.metadata.get('document_id') or sentence_node.metadata.get('doc_id')

            if not article_id:
                print(f"No article ID found in metadata: {sentence_node.metadata}")
                return None

            # Convert to ObjectId if it's a string
            if isinstance(article_id, str):
                try:
                    article_id = ObjectId(article_id)
                except:
                    # If not a valid ObjectId, search by string
                    pass

            # Query MongoDB for the article
            article_doc = await self._mongo_db.documents.find_one({"_id": article_id})
            if not article_doc:
                print(f"No article found for ID: {article_id}")
                return None

            return article_doc

        except Exception as e:
            print(f"Error fetching article from MongoDB: {e}")
            return None

    async def _generate_context(self, article_doc: dict, sentence_text: str) -> str:
        """
        Generate context for the sentence using the full article and LLM.
        """
        try:
            # Get the full article text
            full_article_text = article_doc.get('text', '') or article_doc.get('content', '')

            if not full_article_text:
                return "Context: This sentence is part of a legal document."

            # Create prompt for context generation
            prompt = f"""
            You are analyzing a legal document. Given the full document and a specific sentence from it, provide a brief context (2-3 sentences) that explains:
            1. What section/topic this sentence relates to
            2. Key legal concepts or terms mentioned
            3. How this sentence fits into the broader document

            <full_document>
            {full_article_text[:3000]}...
            </full_document>

            <target_sentence>
            {sentence_text}
            </target_sentence>

            Provide a concise context in Nepali and English:
            """

            response = await self._llm.acomplete(prompt=prompt)
            context = response.text.strip()

            # Ensure context is not too long
            if len(context) > 500:
                context = context[:500] + "..."

            return f"Context: {context}"

        except Exception as e:
            print(f"Error generating context: {e}")
            return "Context: This sentence is part of a legal document."


async def enhance_sentence_chunks_with_context(
    sentence_nodes: List[TextNode],
    mongo_db,
    api_key_config=None,
    batch_size: int = 100
) -> List[TextNode]:
    """
    Convenience function to enhance sentence chunks with context from MongoDB articles.

    Args:
        sentence_nodes: List of sentence-split TextNode objects
        mongo_db: MongoDB database instance
        api_key_config: API key configuration for LLM
        batch_size: Number of nodes to process in each batch

    Returns:
        List of enhanced TextNode objects with context added
    """
    enhancer = SentenceWithPageContext(
        mongo_db=mongo_db,
        batch_size=batch_size,
        api_key_config=api_key_config
    )

    return await enhancer(sentence_nodes)


def create_sentence_with_context_pipeline(mongo_db, api_key_config=None):
    """
    Create a pipeline component for adding context to sentence chunks.

    This can be used in your existing document processing pipeline.

    Args:
        mongo_db: MongoDB database instance
        api_key_config: API key configuration for LLM

    Returns:
        SentenceWithPageContext instance ready to use in pipeline
    """
    return SentenceWithPageContext(
        mongo_db=mongo_db,
        api_key_config=api_key_config
    )

