import json
import numpy as np
import hdbscan
from sklearn.preprocessing import StandardScaler
from sklearn.preprocessing import normalize
import umap
from llama_index.core import Document
from llama_index.llms.gemini import Gemini
from llama_index.core.llms.function_calling import FunctionCallingLLM
from llama_index.embeddings.openai import OpenAIEmbedding
import asyncio
import nest_asyncio
from typing import List
import itertools
import hashlib
import time
nest_asyncio.apply()

class TOCGenerator:
    """ 
    Given a List[Document], Generate a title for each document and embed them
    Create a cluster grouping of the embeddings and for each cluster generate a section name
    add section_name, title and title_embeddings to each document.
    return the documents.  
      
    """
    def __init__(self, documents: List[Document], llm:FunctionCallingLLM):
        self._llm = llm
        self._documents = documents
        self.embedding_model = OpenAIEmbedding(model="text-embedding-3-large", dimensions=1536)
        





    async def _agenerate_title_for_document(self ,doc: Document, llm: FunctionCallingLLM) -> Document:
        """
        Generates a concise title for the document using an LLM.
        """
        # Define a prompt template for title generation
        prompt_template = (
            "Generate a concise and informative title of 5 words or less for the following document.  if the document doesn't contain any meaningful information Label it as `Unknown Document`\n Content:\n\n"
            "{document_text}\n\nTitle:"
        )
        prompt = prompt_template.format(document_text=doc.text)
        # Generate the title using the LLM predictor
        resp = await llm.acomplete(prompt)
        title = resp.text.strip()

        title_embedding = await self.embedding_model.aget_text_embedding(text=title)

        doc.metadata["title"] = title
        doc.metadata["title_embedding"] = title_embedding
        return doc

    def add_titles_to_documents(self,documents, llm: FunctionCallingLLM):
        """
        Iterates over each document, generates a title, and adds it to the document's metadata.
        """
        # tasks = map(lambda doc: self._agenerate_title_for_document(doc=doc, llm=llm), documents)
        # updated_documents = asyncio.run(asyncio.gather(*tasks))

        start = time.time()

        tasks = []
        for d in documents:
            tasks.append(self._agenerate_title_for_document(d, llm))
        updated_documents = asyncio.run(asyncio.gather(*tasks))
        end = time.time()

        print(f"TIME_TAKEN ADD_TITLE: {end-start:.2f}")
        return updated_documents

    def cluster_documents(self, documents, min_cluster_size=2, min_samples=2):
        """
        Clusters document titles based on their embeddings using HDBSCAN.
        
        Args:
            documents (list of dict): List of document dictionaries containing "metadata" with "title_embedding".
            min_cluster_size (int): Minimum number of documents per cluster.
            min_samples (int): How conservative the clustering should be.
            
        Returns:
            dict: Cluster ID mapped to document IDs.
        """
        try:
            start = time.time()
            # Extract embeddings from metadata
            embeddings = np.array([doc.metadata.get("title_embedding") for doc in documents])

            # Standardize embeddings (improves clustering performance)
            embeddings = StandardScaler().fit_transform(embeddings)

            # Perform HDBSCAN clustering
            # clusterer = hdbscan.HDBSCAN(min_cluster_size=min_cluster_size, min_samples=min_samples, metric='euclidean', cluster_selection_method="leaf")
            # cluster_labels = clusterer.fit_predict(embeddings)

            # Normalize embeddings for cosine similarity
            embeddings = normalize(embeddings, axis=1)

            # Get dataset size
            dataset_size = len(embeddings)

            # Set n_neighbors dynamically (use ~10% of dataset, min 2, max 50)
            n_neighbors = max(2, min(50, dataset_size // 10))

            # Use UMAP to project embeddings into a space where Euclidean = Cosine
            reducer = umap.UMAP(n_neighbors=n_neighbors, n_components=5, metric="cosine")
            reduced_embeddings = reducer.fit_transform(embeddings)

            # Perform HDBSCAN clustering
            clusterer = hdbscan.HDBSCAN(
                min_cluster_size=min_cluster_size,
                min_samples=min_samples,
                metric="euclidean",  # Now Euclidean works because of UMAP projection
                cluster_selection_method="leaf",
            )

            cluster_labels = clusterer.fit_predict(reduced_embeddings)

            # Group document IDs by cluster
            clustered_docs = {}
            for doc, cluster_id in zip(documents, cluster_labels):
                if cluster_id == -1:
                    continue  # Ignore noise points (unclustered docs)

                if cluster_id not in clustered_docs:
                    clustered_docs[cluster_id] = []

                clustered_docs[cluster_id].append(doc.doc_id)

            # If no clusters were formed (or if all documents were marked as noise), put them in one cluster
            if not clustered_docs:
                clustered_docs = {0: [doc.doc_id for doc in documents]}

            end = time.time()
            print(f"TIME_TAKEN CLUSTER: {end-start:.2f}")

            return clustered_docs
        except Exception as e:
            clustered_docs = {0: [doc.doc_id for doc in documents]}
            return clustered_docs

    async def generate_section_title(self, titles, llm) -> str:
        """
        Uses an LLM to generate a section title from a list of document titles.
        
        Args:
            titles (list of str): List of document titles in a cluster.
            
        Returns:
            str: The generated section title.
        """
        prompt = f"""
        Given the following document titles, generate a concise and meaningful section title:
        
        {json.dumps(titles, indent=2)}
        
        The section title should be short and descriptive.
        Return only the section title, nothing else.
        """
        response = await llm.acomplete(prompt=prompt)

        return response.text.strip()

    def generate_toc(self):
        """
        Generates a Table of Contents-like structure where each section groups documents.

        Args:
            clustered_docs (dict): Mapping of cluster IDs to document IDs.
            documents (list of dict): List of document dictionaries.

        Returns:
            list of dict: TOC-like structure with section names and associated documents.
        """
        # Create a lookup for document titles
        llm = self._llm
        documents = self._documents

        doc_lookup = {doc.doc_id: doc for doc in documents}

        document_with_titles = self.add_titles_to_documents(documents, llm)

        clustered_docs = self.cluster_documents(document_with_titles)

        toc = []

        start = time.time()


        # for cluster_id, doc_ids in clustered_docs.items():
        #     # Get all titles in the cluster
        #     titles = [doc_lookup[doc_id].metadata.get("title") for doc_id in doc_ids]

        #     # Generate a section title
        #     section_title = self.generate_section_title(titles, llm)


        #     doc_info = [{"id": doc_id, "name": title} for doc_id, title in zip(doc_ids, titles)]





        #     # Append to TOC
        #     toc.append({
        #         "id":hashlib.sha256(section_title.encode('utf-8')).hexdigest(),
        #         "section_name": section_title,
        #         "doc_info": doc_info 
        #     })

        #     # add this section_name to the metadata of all the documents
        #     for doc_id in doc_ids:
        #         doc_lookup[doc_id].metadata["section_title"] = section_title

        async def process_cluster_async(cluster_id, doc_ids, llm):
            # Get all titles in the cluster
            titles = [doc_lookup[doc_id].metadata.get("title") for doc_id in doc_ids]
            
            # Generate a section title asynchronously
            section_title = await self.generate_section_title(titles, llm)
            
            doc_info = [{"id": doc_id, "name": title} for doc_id, title in zip(doc_ids, titles)]
            
            # Compute section ID
            section_id = hashlib.sha256(section_title.encode('utf-8')).hexdigest()
            
            # Add section name to the metadata of all documents
            for doc_id in doc_ids:
                doc_lookup[doc_id].metadata["section_title"] = section_title
            
            return {"id": section_id, "section_name": section_title, "doc_info": doc_info}
        
        tasks = [process_cluster_async(cluster_id, doc_ids, llm) for cluster_id, doc_ids in clustered_docs.items()]
        
        # Run tasks concurrently
        toc = asyncio.run(asyncio.gather(*tasks))


        end = time.time()

        print(f"TIME_TAKEN SECTION_TITLE_ADD: {end-start:.2f}")

        return toc, list(doc_lookup.values())
    


""" 
## Usage

toc_generator = TOCGenerator(
    documents = docs_, llm = Gemini(model="models/gemini-1.5-pro")
)

final_toc, updated_documents = toc_generator.generate_toc()

"""