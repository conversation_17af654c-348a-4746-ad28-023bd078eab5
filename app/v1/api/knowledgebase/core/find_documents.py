from typing import Dict, Any, List
from app.core.utils.response import Paginator, PaginatedResponse
from app.v1.api.knowledgebase.models import FindRequest
from app.v1.api.knowledgebase.core.build_criteria import build_search_criteria


async def find_documents_data(collection, find_request: FindRequest) -> PaginatedResponse:
    """Find documents using single aggregation with facet"""
    # Build search criteria
    criteria = build_search_criteria(find_request)
    
    # Pagination
    paginator = Paginator(page=find_request.page, per_page=find_request.per_page)
    
    # Single aggregation using facet for both count and data
    combined_pipeline: List[Dict[str, Any]] = [
        {"$match": criteria},
        {
            "$facet": {
                "data": [
                    {"$sort": {find_request.sort_by or "created_at": find_request.sort_order}},
                    {"$skip": paginator.skip},
                    {"$limit": paginator.limit}
                ],
                "count": [
                    {"$count": "total"}
                ]
            }
        }
    ]
    
    result = await (await collection.aggregate(combined_pipeline)).to_list(1)
    
    if result:
        data = result[0]
        documents = data.get("data", [])
        total = data.get("count", [{}])[0].get("total", 0)
    else:
        documents = []
        total = 0
    
    return paginator.paginate(
        data=documents,
        total=total,
        message=f"Found {total} documents"
    )
