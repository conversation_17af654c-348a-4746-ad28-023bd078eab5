from typing import Dict, Any
from app.v1.api.knowledgebase.models import FindRequest


def build_search_criteria(find_request: FindRequest) -> Dict[str, Any]:
    """Build MongoDB search criteria from FindRequest"""
    criteria = {}
    
    # Text search
    if find_request.query:
        criteria["$or"] = [
            {"title": {"$regex": find_request.query, "$options": "i"}},
            {"text": {"$regex": find_request.query, "$options": "i"}},
            {"summary": {"$regex": find_request.query, "$options": "i"}},
            {"actual_cleaned_text": {"$regex": find_request.query, "$options": "i"}}
        ]
    
    # Exact match filters
    if find_request.article_id:
        criteria["article_id"] = find_request.article_id
    if find_request.court_type:
        criteria["court_type"] = find_request.court_type
    if find_request.mudda_type:
        criteria["mudda_type"] = find_request.mudda_type
    if find_request.month:
        criteria["month"] = find_request.month
    if find_request.content_extracted is not None:
        criteria["content_extracted"] = find_request.content_extracted
    if find_request.views:
        criteria["views"] = find_request.views
    
    # Date filters
    if find_request.created_at_from or find_request.created_at_to:
        date_filter = {}
        if find_request.created_at_from:
            date_filter["$gte"] = find_request.created_at_from
        if find_request.created_at_to:
            date_filter["$lte"] = find_request.created_at_to
        criteria["created_at"] = date_filter
    
    if find_request.updated_at_from or find_request.updated_at_to:
        date_filter = {}
        if find_request.updated_at_from:
            date_filter["$gte"] = find_request.updated_at_from
        if find_request.updated_at_to:
            date_filter["$lte"] = find_request.updated_at_to
        criteria["updated_at"] = date_filter
    
    return criteria
