from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from app.v1.api.knowledgebase.core.chunking.TOCGenerator import TOCGenerator
from app.v1.api.knowledgebase.core.chunking.flow_preserving_chunker import create_flow_preserving_chunker




router = APIRouter()


@router.post("/insert_document")
async def insert_document():
    """
    Endpoint to insert a document into the knowledge base.
    This will generate titles, embeddings, and chunk the document.
    """
    # Example of how to use TOCGenerator and flow_preserving_chunker
    documents = []