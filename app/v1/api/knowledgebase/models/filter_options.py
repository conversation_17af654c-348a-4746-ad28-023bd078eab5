from pydantic import BaseModel, Field
from typing import List


class FilterOptions(BaseModel):
    """Available filter options for frontend"""
    court_types: List[str] = Field(..., description="Available court types")
    mudda_types: List[str] = Field(..., description="Available mudda types")
    months: List[str] = Field(..., description="Available months")
    article_ids: List[str] = Field(..., description="Available article IDs")
    content_extracted_options: List[bool] = Field(..., description="Available content_extracted values")
