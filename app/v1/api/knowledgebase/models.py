"""
Knowledge base models
"""

from pydantic import BaseModel, Field
from typing import Optional, List


class FilterOptions(BaseModel):
    """Available filter options for frontend"""
    court_types: List[str] = Field(..., description="Available court types")
    mudda_types: List[str] = Field(..., description="Available mudda types")
    months: List[str] = Field(..., description="Available months")
    article_ids: List[str] = Field(..., description="Available article IDs (first 100)")
    content_extracted_options: List[bool] = Field(..., description="Available content_extracted values")


class FindRequest(BaseModel):
    """Find request for POST endpoint"""
    query: Optional[str] = Field(None, description="Search query")
    article_id: Optional[str] = Field(None, description="Filter by article_id")
    court_type: Optional[str] = Field(None, description="Filter by court_type")
    mudda_type: Optional[str] = Field(None, description="Filter by mudda_type")
    month: Optional[str] = Field(None, description="Filter by month")
    content_extracted: Optional[bool] = Field(None, description="Filter by content_extracted")
    created_at_from: Optional[str] = Field(None, description="Filter created_at from date")
    created_at_to: Optional[str] = Field(None, description="Filter created_at to date")
    updated_at_from: Optional[str] = Field(None, description="Filter updated_at from date")
    updated_at_to: Optional[str] = Field(None, description="Filter updated_at to date")
    views: Optional[str] = Field(None, description="Filter by views")
    page: int = Field(1, ge=1, description="Page number")
    per_page: int = Field(10, ge=1, le=100, description="Items per page")
    sort_by: Optional[str] = Field("created_at", description="Field to sort by")
    sort_order: int = Field(-1, description="Sort order: 1 for ascending, -1 for descending")
