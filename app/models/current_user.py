from typing import Any, Optional
from pydantic import BaseModel
from app.models.user import User
from pymongo import MongoClient, AsyncMongoClient


class CurrentUserDB(BaseModel):
    """Organized database access for current user"""
    client: MongoClient  # MongoDB client
    async_client: AsyncMongoClient  # Async MongoDB client
    read_db: Any  # Read database
    write_db: Optional[Any] = None  # Write database (admin only)
    
    class Config:
        arbitrary_types_allowed = True


class CurrentUserQdrant(BaseModel):
    """Organized Qdrant access for current user"""
    qdrant: Optional[Any] = None  # Async Qdrant client
    qdrant_sync: Optional[Any] = None  # Sync Qdrant client
    qd_index: Optional[Any] = None  # Async VectorStoreIndex
    qd_index_sync: Optional[Any] = None  # Sync VectorStoreIndex
    act_index: Optional[Any] = None  # Async VectorStoreIndex for activities
    act_index_sync: Optional[Any] = None  # Sync VectorStoreIndex for activities
    summary_index: Optional[Any] = None  # Async VectorStoreIndex for summaries
    async_summary_index: Optional[Any] = None  # Sync VectorStoreIndex for summaries
    
    class Config:
        arbitrary_types_allowed = True


class CurrentUserLLM(BaseModel):
    """Organized LLM access for current user"""
    openai: Optional[Any] = None  # OpenAI LLM client
    gemini: Optional[Any] = None  # Gemini LLM client
    
    class Config:
        arbitrary_types_allowed = True


class CurrentUser(BaseModel):
    """
    Organized current user structure with clean access patterns:
    - currentuser.user: User information
    - currentuser.db: Database access (client, read_db, write_db)
    - currentuser.qdrant: All Qdrant indexes and clients
    - currentuser.llm: LLM clients and managers
    """
    user: User  # User information
    tenant_id: str
    tenant_name: str
    db: CurrentUserDB  # Database access
    qdrant: CurrentUserQdrant  # Qdrant access
    llm: CurrentUserLLM  # LLM access
    
    class Config:
        arbitrary_types_allowed = True
        
    def has_write_access(self) -> bool:
        """Check if user has write database access"""
        return self.db.write_db is not None
        
    def get_db_for_operation(self, operation_type: str = "read") -> Any:
        """Get appropriate database based on operation type"""
        if operation_type.lower() == "write":
            if self.db.write_db is None:
                from fastapi import HTTPException
                raise HTTPException(
                    status_code=403,
                    detail="Write access denied. Admin role required."
                )
            return self.db.write_db
        return self.db.read_db
