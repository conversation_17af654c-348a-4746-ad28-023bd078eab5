from fastapi import Fast<PERSON><PERSON>, responses
from app.v1.api import v1_api  # must be a FastAPI instance, not just an APIRouter
from fastapi.middleware.cors import CORSMiddleware
app = FastAPI(
    title="Main App",
    description="This is the main app. Sub-apps like /v1 are mounted separately.",
    version="main",
    openapi_version="3.1.0",
    servers=[
        {"url": "/", "description": "Main Server"},
        {"url": "/v1", "description": "API v1"}
    ]
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*","http://localhost:3000","http://localhost:5173","http://localhost:5174"],  # Adjust this to your needs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# Mount versioned sub-apps
app.mount("/v1", v1_api)

@app.get("/", include_in_schema=False)
async def root():
    return responses.RedirectResponse(url="/docs")

@app.get("/hello", tags=["Main"])
async def hello_main():
    return {"message": "Hello from Main App"}
@app.on_event("startup")
async def on_startup():
    print("🚀 Server starting up...")

@app.on_event("shutdown")
async def on_shutdown():
    print("👋 Server shutting down...")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)