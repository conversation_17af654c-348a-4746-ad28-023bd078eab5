"""
Qdrant client for vector database operations
"""

from app.core.logger import StructuredLogger
from typing import Optional
from qdrant_client import AsyncQdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import VectorStoreIndex
from llama_index.core import StorageContext
from llama_index.embeddings.jinaai import JinaEmbedding
from llama_index.embeddings.openai import OpenAIEmbedding

import os
logger = StructuredLogger(__name__)


from qdrant_client import QdrantClient

async def create_qdrant_clients(config_db):
    """Create both async and sync Qdrant clients from config"""
    try:
        # Get Qdrant config from database
        qdrant_config = await config_db.config.find_one({"name": "qdrant_config"})

        if not qdrant_config:
            logger.warning("Qdrant config not found in database")
            return None, None, None, None

        host = qdrant_config["host"]
        port = qdrant_config["port"]
        sent_coll_name = qdrant_config.get("search_coll", "legal_sentence_split")
        act_coll_name = qdrant_config.get("legal_act_coll", "legal_acts_jina")
        summary_coll_name = qdrant_config.get("najir_summary_col", "legal_summary_jina")
        # Create async client and index
        async_client = AsyncQdrantClient(host=host, port=port)
        logger.info(f"Qdrant async client created for {host}:{port}")
        
        # Create sync client and index
        sync_client = QdrantClient(host=host, port=port)
        logger.info(f"Qdrant sync client created for {host}:{port}")
        # embed_model = JinaEmbedding(
        #     api_key=os.getenv("JINA_API_KEY"),
        #     model="jina-embeddings-v4",
        #     embed_batch_size=10,
        #     task="text-matching",
        #     dimensions=2048
        # )
        embed_model = OpenAIEmbedding(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="text-embedding-3-large",
            embed_batch_size=10,
            task="text-matching",
            dimensions=2048
        )
        embed_model1 = OpenAIEmbedding(
            api_key=os.getenv("OPENAI_API_KEY"),
            model="text-embedding-3-large",
            embed_batch_size=10,
            task="text-matching",
            dimensions=1536
        )
        
        # Create async vector store and index
        async_vector_store = QdrantVectorStore(
            aclient=async_client, 
            client=sync_client,
            collection_name=sent_coll_name
        )
        async_storage = StorageContext.from_defaults(vector_store=async_vector_store)
        async_index = VectorStoreIndex.from_vector_store(
            vector_store=async_vector_store,
            storage_context=async_storage,
            embed_model=embed_model1
        )
        
        # Create sync vector store and index
        sync_vector_store = QdrantVectorStore(
            aclient=async_client,
            client=sync_client,
            collection_name=sent_coll_name

        )
        sync_storage = StorageContext.from_defaults(vector_store=sync_vector_store)
        sync_index = VectorStoreIndex.from_vector_store(
            vector_store=sync_vector_store,
            storage_context=sync_storage,
            embed_model=embed_model1
        )
        act_vector_store = QdrantVectorStore(
            aclient=async_client,
            client=sync_client,
            collection_name=act_coll_name
        )
     
        act_storage = StorageContext.from_defaults(vector_store=act_vector_store)
        act_index = VectorStoreIndex.from_vector_store(
            vector_store=act_vector_store,
            storage_context=act_storage,
            embed_model=embed_model
        )
        
        # Create sync act index
        act_sync_vector_store = QdrantVectorStore(
            aclient=async_client,
            client=sync_client,
            collection_name=act_coll_name
        )

      

        act_sync_storage = StorageContext.from_defaults(vector_store=act_sync_vector_store)
        act_sync_index = VectorStoreIndex.from_vector_store(
            vector_store=act_sync_vector_store,
            storage_context=act_sync_storage,
            embed_model=embed_model
        )
        

        # summary index and async summary index

        summary_vector_store = QdrantVectorStore(
            aclient=async_client,
            client=sync_client,
            collection_name=summary_coll_name
        )
        summary_storage = StorageContext.from_defaults(vector_store=summary_vector_store)
        summary_index = VectorStoreIndex.from_vector_store(
            vector_store=summary_vector_store,
            storage_context=summary_storage,
            embed_model=embed_model
        )
        
        async_summary_index = VectorStoreIndex.from_vector_store(
            vector_store=summary_vector_store,
            storage_context=summary_storage,
            embed_model=embed_model
        )
        
        return async_client, sync_client, async_index, sync_index, act_index, act_sync_index,summary_index,async_summary_index

    except Exception as e:
        logger.error(f"Failed to create Qdrant client: {e}")
        return None
 