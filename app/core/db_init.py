"""
Database initialization script for admin and tenant databases
Creates clean setup with role-based access control
"""

import asyncio
import os
from typing import List, Dict, Any
from dotenv import load_dotenv
from app.core.logger import StructuredLogger

from app.core.database import get_read_db, get_write_db
from app.core.security import hash_password
from bson import ObjectId

load_dotenv()

logger = StructuredLogger(__name__)

# Role hierarchy and permissions
ROLE_HIERARCHY = {
    "admin": 3,
    "lawyer": 3,  # Same level as admin
    "user": 1
}

DEFAULT_PERMISSIONS = {
    "admin": [
        "create_user", "read_user", "update_user", "delete_user",
        "create_role", "read_role", "update_role", "delete_role",
        "create_permission", "read_permission", "update_permission", "delete_permission",
        "manage_tenant", "read_tenant", "write_tenant",
        "view_analytics", "export_data", "system_config","prompt_management",
        "query", "chat", "basic_operations"
    ],
    "lawyer": [
        # Same permissions as admin - for lawyers
        "create_user", "read_user", "update_user", "delete_user",
        "create_role", "read_role", "update_role", "delete_role",
        "create_permission", "read_permission", "update_permission", "delete_permission",
        "manage_tenant", "read_tenant", "write_tenant",
        "view_analytics", "export_data", "system_config", 
        "query", "chat", "basic_operations"
    ],
    "user": [
        "read_user", "read_role", "read_permission",
        "read_tenant", "basic_operations","query","chat"
    ]
}

DUMMY_USERS = [
    {
        "username": "admin",
        "password": "admin123",
        "roles": ["admin"],
        "email": "<EMAIL>"
    },
    {
        "username": "lawyer",
        "password": "lawyer123",
        "roles": ["lawyer"],
        "email": "<EMAIL>"
    },
    {
        "username": "user",
        "password": "user123",
        "roles": ["user"],
        "email": "<EMAIL>"
    }
]

class DatabaseInitializer:
    """Clean database initialization with async operations"""
    
    def __init__(self):
        self.project_name = self._get_project_name()
        self.admin_db_name = f"{self.project_name}_admin"
        self.secret_key = self._get_secret_key()
        
    def _get_project_name(self) -> str:
        """Get project name from environment"""
        project_name = os.getenv("PROJECT_NAME")
        if not project_name:
            raise ValueError("PROJECT_NAME environment variable not set")
        return project_name.lower()
    
    def _get_secret_key(self) -> str:
        """Get secret key from environment"""
        secret_key = os.getenv("SECRET_KEY")
        if not secret_key:
            raise ValueError("SECRET_KEY environment variable not set")
        return secret_key
    
    async def initialize_admin_database(self) -> None:
        """Initialize admin database with clean tenant structure"""
        try:
            admin_db = get_write_db(self.admin_db_name)

            logger.info(f"Initializing admin database: {self.admin_db_name}")

            # Create collections if they don't exist
            collections = await admin_db.list_collection_names()
            required_collections = ["tenants"]

            for collection_name in required_collections:
                if collection_name not in collections:
                    await admin_db.create_collection(collection_name)
                    logger.info(f"Created collection: {collection_name}")

            # Create indexes for performance
            await admin_db.tenants.create_index("slug", unique=True)
            await admin_db.tenants.create_index("db_name", unique=True)

            logger.info("Admin database initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize admin database: {e}")
            raise
    
    async def create_tenant(self, tenant_name: str, slug: str) -> str:
        """Create a new tenant with clean database structure"""
        try:
            admin_db = get_write_db(self.admin_db_name)

            # Clean tenant name and create database name
            clean_name = tenant_name.lower().replace(" ", "_")
            db_name = f"{clean_name}_{self.project_name}_db"

            # Check if tenant already exists
            existing_tenant = await admin_db.tenants.find_one({"slug": slug})
            if existing_tenant:
                logger.warning(f"Tenant with slug '{slug}' already exists")
                return str(existing_tenant["_id"])

            # Create tenant document (clean structure)
            tenant_doc = {
                "name": tenant_name,
                "slug": slug,
                "db_name": db_name,
                "created_at": ObjectId().generation_time,
                "status": "active"
            }

            result = await admin_db.tenants.insert_one(tenant_doc)
            tenant_id = str(result.inserted_id)

            # Initialize tenant database
            await self._initialize_tenant_database(db_name)

            logger.info(f"Created tenant: {tenant_name} (ID: {tenant_id})")
            return tenant_id

        except Exception as e:
            logger.error(f"Failed to create tenant: {e}")
            raise
    
    async def _initialize_tenant_database(self, db_name: str) -> None:
        """Initialize tenant database with required collections"""
        try:
            tenant_db = get_write_db(db_name)
            
            # Required collections for tenant
            required_collections = [
                "users", "roles", "permissions", "invitations", "config"
            ]
            
            collections = await tenant_db.list_collection_names()
            for collection_name in required_collections:
                if collection_name not in collections:
                    await tenant_db.create_collection(collection_name)
            
            # Create indexes
            await tenant_db.users.create_index("username", unique=True)
            await tenant_db.roles.create_index("name", unique=True)
            await tenant_db.permissions.create_index("name", unique=True)
            
            # Initialize permissions
            await self._create_permissions(tenant_db)
            
            # Initialize roles
            await self._create_roles(tenant_db)
            
            # Create dummy users
            await self._create_dummy_users(tenant_db)
            
            logger.info(f"Tenant database {db_name} initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize tenant database: {e}")
            raise
    
    async def _create_permissions(self, tenant_db) -> None:
        """Create default permissions"""
        all_permissions = set()
        for role_perms in DEFAULT_PERMISSIONS.values():
            all_permissions.update(role_perms)
        
        for perm_name in all_permissions:
            permission_doc = {
                "name": perm_name,
                "description": f"Permission for {perm_name.replace('_', ' ')}",
                "tags": [perm_name.split('_')[0]]  # First part as tag
            }
            await tenant_db.permissions.update_one(
                {"name": perm_name},
                {"$set": permission_doc},
                upsert=True
            )
    
    async def _create_roles(self, tenant_db) -> None:
        """Create default roles with permissions"""
        for role_name, permissions in DEFAULT_PERMISSIONS.items():
            role_doc = {
                "name": role_name,
                "default_permissions": permissions,
                "hierarchy_level": ROLE_HIERARCHY[role_name]
            }
            await tenant_db.roles.update_one(
                {"name": role_name},
                {"$set": role_doc},
                upsert=True
            )
    
    async def _create_dummy_users(self, tenant_db) -> None:
        """Create dummy users for testing"""
        for user_data in DUMMY_USERS:
            # Get permissions for user roles
            user_permissions = []
            for role in user_data["roles"]:
                user_permissions.extend(DEFAULT_PERMISSIONS.get(role, []))
            
            # Remove duplicates
            user_permissions = list(set(user_permissions))
            
            user_doc = {
                "username": user_data["username"],
                "hashed_password": hash_password(user_data["password"]),
                "roles": user_data["roles"],
                "permissions": user_permissions,
                "email": user_data["email"],
                "created_at": ObjectId().generation_time,
                "status": "active"
            }
            
            await tenant_db.users.update_one(
                {"username": user_data["username"]},
                {"$set": user_doc},
                upsert=True
            )
            
            logger.info(f"Created dummy user: {user_data['username']}")

async def initialize_system():
    """Main initialization function"""
    try:
        initializer = DatabaseInitializer()
        
        # Initialize admin database
        await initializer.initialize_admin_database()
        
        # Create default tenant
        tenant_id = await initializer.create_tenant("Default Company", "default")
        
        logger.info("System initialization completed successfully")
        logger.info(f"Default tenant ID: {tenant_id}")
        logger.info("Dummy users created:")
        for user in DUMMY_USERS:
            logger.info(f"  - {user['username']} (roles: {', '.join(user['roles'])})")

    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(initialize_system())
