"""
Authentication helper functions for async operations
"""

from app.core.logger import StructuredLogger
from typing import Optional, List, Dict, Any
from fastapi import HTTPException
from bson import ObjectId

from app.core.database import get_user_databases_async, get_read_db
from app.core.security import hash_password, verify_password
from app.models.user import User
from app.models.current_user import CurrentUser, CurrentUserDB, CurrentUserQdrant, CurrentUserLLM
from app.models.permission import Permission
from app.core.qdrant_client import create_qdrant_clients
from app.core.llm_client import create_llm_clients

logger = StructuredLogger(__name__)

class AuthHelper:
    """Helper class for authentication operations"""
    
    @staticmethod
    async def authenticate_user(username: str, password: str, tenant_slug: str) -> Optional[CurrentUser]:
        """Authenticate user with async database operations"""
        try:
            # Get tenant info from slug
            from app.core.database import _get_project_name
            admin_db_name = f"{_get_project_name()}_admin"
            admin_db = get_read_db(admin_db_name)
            tenant_doc = await admin_db.tenants.find_one({"slug": tenant_slug})

            if not tenant_doc:
                logger.warning(f"Tenant not found for slug: {tenant_slug}")
                return None

            tenant_id = str(tenant_doc["_id"])
            
            # Get user databases (read-only for authentication)
            user_dbs = await get_user_databases_async(tenant_id)
            read_db = user_dbs["read_db"]
            
            # Find user
            user_doc = await read_db.users.find_one({"username": username})
            if not user_doc:
                logger.warning(f"User not found: {username}")
                return None
            
            # Verify password
            if not verify_password(password, user_doc["hashed_password"]):
                logger.warning(f"Invalid password for user: {username}")
                return None
            
            # Get user permissions
            user_permissions = []
            for permission_name in user_doc.get("permissions", []):
                perm_doc = await read_db.permissions.find_one({"name": permission_name})
                if perm_doc:
                    user_permissions.append(Permission(**perm_doc))
            
            # Update user document with permissions
            user_doc["permissions"] = user_permissions
            
            # Create User object
            user = User(**user_doc)
            
            # Get databases with proper role-based access
            user_dbs_with_roles = await get_user_databases_async(tenant_id=tenant_id, user_role=user.role)

            # Initialize Qdrant and LLM clients
            qdrant_async, qdrant_sync, qd_index_async, qd_index_sync, act_index, act_sync_index, summary_index, async_summary_index = await create_qdrant_clients(user_dbs_with_roles["read_db"])
            llm_clients = await create_llm_clients(user_dbs_with_roles["read_db"])

            return CurrentUser(
                user=user,
                tenant_id=tenant_id,
                tenant_name=user_dbs_with_roles["tenant_name"],
                db=CurrentUserDB(
                    client=user_dbs_with_roles["client"],
                    async_client=user_dbs_with_roles["async_client"],
                    read_db=user_dbs_with_roles["read_db"],
                    write_db=user_dbs_with_roles["write_db"]
                ),
                qdrant=CurrentUserQdrant(
                    qdrant=qdrant_async,
                    qdrant_sync=qdrant_sync,
                    qd_index=qd_index_async,
                    qd_index_sync=qd_index_sync,
                    act_index=act_index,
                    act_index_sync=act_sync_index,
                    summary_index=summary_index,
                    async_summary_index=async_summary_index
                ),
                llm=CurrentUserLLM(llm=llm_clients)
            )
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    @staticmethod
    async def create_user(
        username: str, 
        password: str, 
        roles: List[str], 
        tenant_id: str,
        creator_user: CurrentUser,
        email: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a new user with role-based permissions"""
        try:
            # Check if creator has write access
            if not creator_user.has_write_access():
                raise HTTPException(
                    status_code=403,
                    detail="Write access required to create users"
                )
            
            write_db = creator_user.get_write_db()
            read_db = creator_user.read_db
            
            # Check if user already exists
            existing_user = await read_db.users.find_one({"username": username})
            if existing_user:
                raise HTTPException(
                    status_code=400,
                    detail="Username already exists"
                )
            
            # Validate roles
            valid_roles = []
            for role_name in roles:
                role_doc = await read_db.roles.find_one({"name": role_name})
                if not role_doc:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid role: {role_name}"
                    )
                valid_roles.append(role_name)
            
            # Get permissions for roles
            user_permissions = []
            for role_name in valid_roles:
                role_doc = await read_db.roles.find_one({"name": role_name})
                if role_doc:
                    user_permissions.extend(role_doc.get("default_permissions", []))
            
            # Remove duplicates
            user_permissions = list(set(user_permissions))
            
            # Create user document
            user_doc = {
                "username": username,
                "hashed_password": hash_password(password),
                "roles": valid_roles,
                "permissions": user_permissions,
                "email": email,
                "created_at": ObjectId().generation_time,
                "created_by": creator_user.user.username,
                "status": "active"
            }
            
            # Insert user
            result = await write_db.users.insert_one(user_doc)
            
            # Log the operation
            creator_user.log_access("create_user", "users")
            
            logger.info(f"User created: {username} by {creator_user.user.username}")
            
            return {
                "id": str(result.inserted_id),
                "username": username,
                "roles": valid_roles,
                "permissions": user_permissions,
                "status": "created"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise HTTPException(status_code=500, detail="User creation failed")
    
    @staticmethod
    async def update_user_roles(
        username: str,
        new_roles: List[str],
        tenant_id: str,
        updater_user: CurrentUser
    ) -> Dict[str, Any]:
        """Update user roles and permissions"""
        try:
            # Check if updater has write access
            if not updater_user.has_write_access():
                raise HTTPException(
                    status_code=403,
                    detail="Write access required to update users"
                )
            
            write_db = updater_user.get_write_db()
            read_db = updater_user.read_db
            
            # Find user
            user_doc = await read_db.users.find_one({"username": username})
            if not user_doc:
                raise HTTPException(
                    status_code=404,
                    detail="User not found"
                )
            
            # Validate new roles
            valid_roles = []
            for role_name in new_roles:
                role_doc = await read_db.roles.find_one({"name": role_name})
                if not role_doc:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid role: {role_name}"
                    )
                valid_roles.append(role_name)
            
            # Get new permissions for roles
            new_permissions = []
            for role_name in valid_roles:
                role_doc = await read_db.roles.find_one({"name": role_name})
                if role_doc:
                    new_permissions.extend(role_doc.get("default_permissions", []))
            
            # Remove duplicates
            new_permissions = list(set(new_permissions))
            
            # Update user
            update_result = await write_db.users.update_one(
                {"username": username},
                {
                    "$set": {
                        "roles": valid_roles,
                        "permissions": new_permissions,
                        "updated_at": ObjectId().generation_time,
                        "updated_by": updater_user.user.username
                    }
                }
            )
            
            if update_result.modified_count == 0:
                raise HTTPException(
                    status_code=400,
                    detail="No changes made to user"
                )
            
            # Log the operation
            updater_user.log_access("update_user_roles", "users")
            
            logger.info(f"User roles updated: {username} by {updater_user.user.username}")
            
            return {
                "username": username,
                "roles": valid_roles,
                "permissions": new_permissions,
                "status": "updated"
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating user roles: {e}")
            raise HTTPException(status_code=500, detail="User update failed")
    
    @staticmethod
    async def get_tenant_info_by_slug(slug: str) -> Dict[str, Any]:
        """Get tenant information by slug"""
        try:
            from app.core.database import _get_project_name
            admin_db_name = f"{_get_project_name()}_admin"
            admin_db = get_read_db(admin_db_name)
            tenant_doc = await admin_db.tenants.find_one(
                {"slug": slug},
                {"_id": 1, "name": 1, "slug": 1, "status": 1}
            )

            if not tenant_doc:
                raise HTTPException(
                    status_code=404,
                    detail="Tenant not found"
                )

            return {
                "tenant_id": str(tenant_doc["_id"]),
                "tenant_name": tenant_doc["name"],
                "tenant_slug": tenant_doc["slug"],
                "status": tenant_doc.get("status", "unknown")
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting tenant info: {e}")
            raise HTTPException(status_code=500, detail="Tenant lookup failed")
