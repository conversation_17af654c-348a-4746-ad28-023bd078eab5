# src/core/security.py

import asyncio
from datetime import datetime, timedelta
from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError
# from passlib.context import CryptContext
from fastapi.security import OAuth2<PERSON><PERSON>word<PERSON>earer
from fastapi import Depends, HTTPException
# Initialize Qdrant and LLM clients
from app.core.qdrant_client import create_qdrant_clients
from app.core.llm_client import create_llm_clients
import jwt
from app.core.config import SECRET_KEY, ALGORITHM
from app.models.user import User
from app.models.current_user import CurrentUser, CurrentUserDB, CurrentUserQdrant, CurrentUserLLM
from app.models.permission import Permission
from app.models.role import Role
from app.core.database import get_user_databases_async, get_db_from_tenant_id
from typing import Optional, List, Callable, Any
from types import CoroutineType

ph = PasswordHasher()
# pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="login",
    scheme_name="Bearer Token",
    description="Use default credentials: username=admin, password=admin123, client_id=legal_backend",
    auto_error=False,  # Don't automatically return 401, let us handle it
)

def create_access_token(data: dict, expires_delta:timedelta = timedelta(days=1)) -> str:
    to_encode = data.copy() 
    if expires_delta:   
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_tenant_info(token: str = Depends(dependency=oauth2_scheme)) -> CurrentUser:    
    """Get user tenant information with appropriate database access based on roles"""
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(jwt=token, key=SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        tenant_id: str = payload.get("tenant_id")

        if not username or not tenant_id:
            raise credentials_exception

    except jwt.PyJWTError:
        raise credentials_exception

    try:
        # Get user databases with role-based access
        user_dbs = await get_user_databases_async(tenant_id=tenant_id)
        read_db = user_dbs["read_db"]

        # Find user in read database
        user_doc = await read_db.users.find_one({"username": username})
        if not user_doc:
            raise credentials_exception

        # Get user permissions
        user_permissions = []
        for permission_name in user_doc.get("permissions", []):
            perm_doc = await read_db.permissions.find_one({"name": permission_name})
            if perm_doc:
                user_permissions.append(Permission(**perm_doc))

        # Update user document with permissions
        user_doc["permissions"] = user_permissions

        # Create User object
        user = User(**user_doc)

        # Get databases with proper role-based access
        user_dbs_with_roles = await get_user_databases_async(tenant_id=tenant_id, user_role=user.role)

      
        # Initialize Qdrant clients and LLM clients in parallel
        qdrant_task = create_qdrant_clients(user_dbs_with_roles["read_db"])
        llm_task = create_llm_clients(user_dbs_with_roles["read_db"])
        
        # Run both tasks concurrently
        qdrant_result, llm_clients = await asyncio.gather(qdrant_task, llm_task)
        
        if any(client is None for client in qdrant_result[:4]):  # Check only the first 4 required clients
            raise HTTPException(
                status_code=500,
                detail="Failed to initialize Qdrant vector database connection"
            )
            
        # Unpack all return values including act indices
        qdrant_async, qdrant_sync, qd_index_async, qd_index_sync, act_index, act_index_sync, summary_index, async_summary_index = qdrant_result

        # Create organized current user structure
        return CurrentUser(
            user=user,
            tenant_id=tenant_id,
            tenant_name=user_dbs_with_roles["tenant_name"],
            db=CurrentUserDB(
                client=user_dbs_with_roles["client"],
                async_client=user_dbs_with_roles["async_client"],
                read_db=user_dbs_with_roles["read_db"],
                write_db=user_dbs_with_roles["write_db"]
            ),
            qdrant=CurrentUserQdrant(
                qdrant=qdrant_async,
                qdrant_sync=qdrant_sync,
                qd_index=qd_index_async,
                qd_index_sync=qd_index_sync,
                act_index=act_index,
                act_index_sync=act_index_sync,
                summary_index=summary_index,
                async_summary_index=async_summary_index
            ),
            llm=CurrentUserLLM(
                openai=llm_clients.openai,
                gemini=llm_clients.gemini
            )
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")


def require_roles(required_roles: List[str]):
    """Dependency to check if user has any of the required roles - FLEXIBLE"""
    async def check_roles(current_user: CurrentUser = Depends(get_tenant_info)):
        if not current_user.user.has_any_role(required_roles):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required roles: {', '.join(required_roles)}"
            )
        return current_user
    return check_roles

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        return username, invited_by, role
    
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


# Replace CryptContext with PasswordHasher
def hash_password(password: str) -> str:
    """
    Hash new passwords using Argon2
    """
    return ph.hash(password)
    
# Update the verify_password function
def verify_password(plain_password, hashed_password) -> bool:
    """
    Verifies a plain password against a hashed password.
    """
    try:
        return ph.verify(hash=hashed_password, password=plain_password)
    except VerifyMismatchError:
        return False


def require_permissions(required_permissions: list[str]) :
    """
    Dependency that checks if the user has all the required permissions.
    Usage: @router.get("/endpoint", dependencies=[Depends(require_permissions(["read:users", "write:users"]))])
    """
    async def check_permissions(current_user: CurrentUser = Depends(dependency=get_tenant_info)) -> CurrentUser:
        user_permissions = [p.name for p in current_user.user.permissions]
        
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return current_user
    return check_permissions


