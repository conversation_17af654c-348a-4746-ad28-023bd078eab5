"""
Advanced auto-detecting logger for the legal backend application.
Auto-detects file, function, process ID, port, and provides multiple log levels.
"""

import logging
import os
import sys
import threading
from datetime import datetime
from typing import Optional, Any
import psutil


class AdvancedFormatter(logging.Formatter):
    """Advanced formatter with auto-detection and colors"""

    # Color codes for different log types
    COLORS = {
        'TEST': '\033[95m',      # Bright Magenta
        'DEBUG': '\033[36m',     # <PERSON>an
        'LOG': '\033[32m',       # Green (INFO)
        'ISSUE': '\033[33m',     # Yellow (WARNING)
        'EXCEPTION': '\033[31m', # Red (ERROR)
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m'       # Reset
    }

    def format(self, record):
        # Get basic info from record
        if hasattr(record, 'pathname') and record.pathname:
            # Show relative path if available, otherwise just filename
            if '/' in record.pathname and not record.pathname.startswith('/'):
                # It's already a relative path
                filepath = record.pathname.replace('.py', '')
            else:
                # Just use filename
                filepath = os.path.basename(record.pathname).replace('.py', '')
        else:
            filepath = "unknown"

        function_name = record.funcName if hasattr(record, 'funcName') else "unknown"
        line_number = record.lineno if hasattr(record, 'lineno') else 0

        # Get process info
        try:
            process = psutil.Process()
            process_id = process.pid
            port = self._detect_port()
        except Exception:
            process_id = os.getpid()
            port = "unknown"

        # Thread info
        thread_id = threading.get_ident()
        thread_name = threading.current_thread().name

        # Get timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        # Add colors
        log_type = getattr(record, 'log_type', record.levelname)
        color = self.COLORS.get(log_type, self.COLORS['RESET'])
        reset = self.COLORS['RESET']

        # Format the message with cleaner thread info
        thread_info = f"{thread_name}" if thread_name == "MainThread" else f"{thread_name}({thread_id})"

        formatted_msg = (
            f"{color}[{timestamp}] "
            f"PID:{process_id} PORT:{port} "
            f"THREAD:{thread_info} "
            f"{log_type} "
            f"{filepath}:{function_name}:{line_number} "
            f"→ {record.getMessage()}{reset}"
        )

        return formatted_msg

    def _detect_port(self):
        """Try to detect the port the application is running on"""
        try:
            # Check environment variables first
            port = os.getenv('PORT') or os.getenv('UVICORN_PORT') or os.getenv('FASTAPI_PORT')
            if port:
                return port

            # Try to find listening ports for this process
            process = psutil.Process()
            connections = process.net_connections(kind='inet')
            for conn in connections:
                if conn.status == 'LISTEN':
                    return conn.laddr.port

            return "unknown"
        except Exception:
            return "unknown"


class StructuredLogger:
    """Advanced auto-detecting logger with multiple log types"""

    def __init__(self, name: str = "legal_backend"):
        # Extract module name from full path if __name__ is passed
        if "." in name:
            self.module_name = name.split(".")[-1]  # Get last part (filename)
            self.full_name = name
        else:
            self.module_name = name
            self.full_name = name

        self.logger = logging.getLogger(self.full_name)
        self.logger.setLevel(logging.DEBUG)

        # Prevent duplicate handlers
        if self.logger.handlers:
            self.logger.handlers.clear()

        self._setup_console_handler()

    def _setup_console_handler(self):
        """Setup console handler with advanced formatting"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(AdvancedFormatter())
        self.logger.addHandler(console_handler)
    
    def _create_record(self, level, message, log_type, extra=None):
        """Create a proper LogRecord with caller information"""
        import inspect

        # Get caller frame (skip this method and the calling logger method)
        frame = inspect.currentframe()
        try:
            caller_frame = frame.f_back.f_back  # Skip _create_record and logger method
            if caller_frame:
                pathname = caller_frame.f_code.co_filename
                lineno = caller_frame.f_lineno
                funcName = caller_frame.f_code.co_name

                # Handle async functions - check if it's a coroutine
                try:
                    # Check if the function is async by looking at code flags
                    if caller_frame.f_code.co_flags & 0x80:  # CO_COROUTINE flag
                        if not funcName.startswith('async '):
                            funcName = f"async {funcName}"
                except:
                    pass

                # Get relative file path from project root
                try:
                    # Try to get relative path from the project root
                    import os
                    current_dir = os.getcwd()
                    if pathname.startswith(current_dir):
                        pathname = os.path.relpath(pathname, current_dir)
                except:
                    # Fallback to just filename
                    pathname = os.path.basename(pathname)
            else:
                pathname = "unknown"
                lineno = 0
                funcName = "unknown"
        except:
            pathname = "unknown"
            lineno = 0
            funcName = "unknown"
        finally:
            del frame

        # Create record with proper caller info
        record = self.logger.makeRecord(
            self.logger.name, level, pathname, lineno, message, (),
            sys.exc_info() if log_type == "EXCEPTION" else None,
            funcName, extra
        )
        record.log_type = log_type
        return record

    def test(self, message: str, extra: Optional[dict] = None):
        """Test level logging for developers"""
        record = self._create_record(logging.DEBUG, message, "TEST", extra)
        self.logger.handle(record)

    def debug(self, message: str, extra: Optional[dict] = None):
        """Debug level logging for detailed information"""
        record = self._create_record(logging.DEBUG, message, "DEBUG", extra)
        self.logger.handle(record)

    def log(self, message: str, extra: Optional[dict] = None):
        """General log level for normal operations"""
        record = self._create_record(logging.INFO, message, "LOG", extra)
        self.logger.handle(record)

    def issue(self, message: str, extra: Optional[dict] = None):
        """Issue level logging for problems that need attention"""
        record = self._create_record(logging.WARNING, message, "ISSUE", extra)
        self.logger.handle(record)

    def exception(self, message: str, extra: Optional[dict] = None):
        """Exception level logging for errors and exceptions"""
        import traceback
        import sys
        
        # Get full exception info
        exc_type, exc_value, exc_traceback = sys.exc_info()
        if exc_type is not None:
            # Format the full traceback
            tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)
            full_traceback = ''.join(tb_lines)
            
            # Get the actual line where error occurred
            tb = exc_traceback
            while tb.tb_next:
                tb = tb.tb_next
            
            error_filename = tb.tb_frame.f_code.co_filename
            error_line = tb.tb_lineno
            error_function = tb.tb_frame.f_code.co_name
            
            # Create enhanced error message
            enhanced_message = f"{message}\n"
            enhanced_message += f"  ERROR LOCATION: {error_filename}:{error_line} in {error_function}()\n"
            enhanced_message += f"  EXCEPTION: {exc_type.__name__}: {exc_value}\n"
            enhanced_message += f"  FULL TRACEBACK:\n{full_traceback}"
            
            # Add to extra for structured logging
            if extra is None:
                extra = {}
            extra.update({
                'error_type': exc_type.__name__,
                'error_message': str(exc_value),
                'error_file': error_filename,
                'error_line': error_line,
                'error_function': error_function,
                'full_traceback': full_traceback
            })
            
            record = self._create_record(logging.ERROR, enhanced_message, "EXCEPTION", extra)
        else:
            # No exception context, just log the message
            record = self._create_record(logging.ERROR, message, "EXCEPTION", extra)
        
        self.logger.handle(record)

    # Backward compatibility methods for old logging calls
    def info(self, message: str, extra: Optional[dict] = None):
        """Alias for log method - backward compatibility"""
        self.log(message, extra)

    def error(self, message: str, extra: Optional[dict] = None):
        """Enhanced error logging with context"""
        import traceback
        import sys
        
        # Check if we're in an exception context
        exc_type, exc_value, exc_traceback = sys.exc_info()
        if exc_type is not None:
            # Use exception method for full traceback
            self.exception(message, extra)
        else:
            # Get caller info for context
            try:
                frame = sys._getframe(2)
                filename = frame.f_code.co_filename
                line_number = frame.f_lineno
                function_name = frame.f_code.co_name
                
                enhanced_message = f"{message}\n"
                enhanced_message += f"  LOCATION: {filename}:{line_number} in {function_name}()"
                
                if extra is None:
                    extra = {}
                extra.update({
                    'error_file': filename,
                    'error_line': line_number,
                    'error_function': function_name
                })
                
                record = self._create_record(logging.ERROR, enhanced_message, "ERROR", extra)
                self.logger.handle(record)
            except:
                # Fallback to basic error logging
                record = self._create_record(logging.ERROR, message, "ERROR", extra)
                self.logger.handle(record)
            finally:
                del frame

    def warning(self, message: str, extra: Optional[dict] = None):
        """Alias for issue method - backward compatibility"""
        self.issue(message, extra)
    
    
    def log_request(self, method: str, path: str, status_code: int, duration: float):
        """Log HTTP requests"""
        self.log(f"{method} {path} - {status_code} - {duration:.3f}s")

    def log_query(self, query: str, results_count: int, duration: float):
        """Log search queries"""
        self.log(f"Query: '{query}' - {results_count} results - {duration:.3f}s")

    def log_retrieval(self, method: str, query: str, chunks: int, duration: float):
        """Log retrieval operations"""
        self.log(f"Retrieval [{method}]: '{query}' - {chunks} chunks - {duration:.3f}s")

    def log_auth(self, user_id: str, action: str, success: bool):
        """Log authentication events"""
        status = "SUCCESS" if success else "FAILED"
        self.log(f"Auth [{action}]: User {user_id} - {status}")

    def log_database(self, operation: str, collection: str, duration: float):
        """Log database operations"""
        self.debug(f"DB [{operation}]: {collection} - {duration:.3f}s")

    def log_exception_with_context(self, message: str, context: Optional[dict] = None):
        """Enhanced exception logging with custom context"""
        import traceback
        import sys
        
        exc_type, exc_value, exc_traceback = sys.exc_info()
        if exc_type is None:
            self.error(message, context)
            return
            
        # Get the actual error location
        tb = exc_traceback
        while tb.tb_next:
            tb = tb.tb_next
        
        error_context = {
            'error_type': exc_type.__name__,
            'error_message': str(exc_value),
            'error_file': tb.tb_frame.f_code.co_filename,
            'error_line': tb.tb_lineno,
            'error_function': tb.tb_frame.f_code.co_name,
            'full_traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        }
        
        if context:
            error_context.update(context)
            
        self.exception(message, error_context)

    def log_error_with_context(self, message: str, context: Optional[dict] = None):
        """Log error with caller context even without exception"""
        import sys
        
        try:
            frame = sys._getframe(2)  # Get caller's frame
            error_context = {
                'error_file': frame.f_code.co_filename,
                'error_line': frame.f_lineno,
                'error_function': frame.f_code.co_name
            }
            
            if context:
                error_context.update(context)
                
            self.error(message, error_context)
        finally:
            del frame
