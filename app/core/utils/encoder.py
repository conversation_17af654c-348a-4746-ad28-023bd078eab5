"""
Simple encoder utility that automatically handles ObjectId conversion
"""

from typing import Any, Dict, List, Union
from bson import ObjectId


class AutoEncoder:
    """
    Automatic encoder class that handles all ObjectId conversions
    Use this class to automatically convert ObjectIds without manual intervention
    """
    
    @staticmethod
    def encode(data: Any) -> Any:
        """
        Automatically encode any data structure, converting ObjectIds to strings
        
        Args:
            data: Any data structure (dict, list, class instance, primitive)
            
        Returns:
            Data structure with ObjectIds automatically converted to strings
        """
        if isinstance(data, ObjectId):
            return str(data)
        elif isinstance(data, dict):
            return AutoEncoder._encode_dict(data)
        elif isinstance(data, list):
            return [AutoEncoder.encode(item) for item in data]
        elif isinstance(data, tuple):
            return tuple(AutoEncoder.encode(item) for item in data)
        elif hasattr(data, '__dict__'):
            # Handle class instances
            return AutoEncoder._encode_object(data)
        else:
            return data
    
    @staticmethod
    def _encode_dict(data: Dict[str, Any]) -> Dict[str, Any]:
        """Encode dictionary, handling _id field specially"""
        encoded = {}
        for key, value in data.items():
            if key == "_id" and isinstance(value, ObjectId):
                encoded["id"] = str(value)
            else:
                encoded[key] = AutoEncoder.encode(value)
        return encoded
    
    @staticmethod
    def _encode_object(obj: Any) -> Dict[str, Any]:
        """Encode class instance to dictionary"""
        if hasattr(obj, 'model_dump'):
            # Pydantic model
            return AutoEncoder.encode(obj.model_dump())
        elif hasattr(obj, 'dict'):
            # Pydantic model (older versions)
            return AutoEncoder.encode(obj.dict())
        elif hasattr(obj, '__dict__'):
            # Regular class instance
            return AutoEncoder.encode(obj.__dict__)
        else:
            return obj


# Convenience function for easy usage
def auto_encode(data: Any) -> Any:
    """
    Automatically encode data with ObjectId conversion
    
    Args:
        data: Any data structure
        
    Returns:
        Data with ObjectIds converted to strings
    """
    return AutoEncoder.encode(data)
