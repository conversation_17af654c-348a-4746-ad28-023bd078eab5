"""
Core module for handling ObjectId in Pydantic models.
"""
from pydantic import PlainSerializer, Field
from typing import Annotated, Any
from bson import ObjectId

# Define a serializer for ObjectId
def object_id_serializer(obj_id: ObjectId) -> str:
    """Serialize ObjectId to string."""
    return str(obj_id)

# Create an annotated type for ObjectId
PyObjectId = Annotated[
    ObjectId,
    PlainSerializer(object_id_serializer)
]

# Helper function to create an ObjectId field with alias="_id"
def object_id_field(*, default_factory=ObjectId, **kwargs):
    """
    Create a Field with PyObjectId type and default alias="_id".

    Args:
        default_factory: Factory function for default value (default: ObjectId)
        **kwargs: Additional field parameters

    Returns:
        Field object configured for ObjectId
    """
    return Field(default_factory=default_factory, alias="_id", **kwargs)

# Validator for ObjectId fields
def validate_object_id(v: Any) -> ObjectId:
    """
    Validate and convert string to ObjectId.

    Args:
        v: Value to validate

    Returns:
        ObjectId instance
    """
    if isinstance(v, str):
        return ObjectId(v)
    return v
