"""
Simple paginated response utility with automatic ObjectId serialization
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field
from math import ceil
from .encoder import auto_encode


class PaginationMeta(BaseModel):
    """Pagination metadata"""
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")
    total: int = Field(..., description="Total number of items")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")


class PaginatedResponse(BaseModel):
    """Paginated response with automatic ObjectId serialization"""
    message: str = Field(..., description="Response message")
    data: List[Dict[str, Any]] = Field(..., description="Paginated data items")
    meta: PaginationMeta = Field(..., description="Pagination metadata")


class Paginator:
    """Simple pagination utility with automatic ObjectId serialization"""

    def __init__(self, page: int = 1, per_page: int = 10, max_per_page: int = 100):
        self.page = max(1, page)
        self.per_page = min(max(1, per_page), max_per_page)

    @property
    def skip(self) -> int:
        """Calculate skip for MongoDB"""
        return (self.page - 1) * self.per_page

    @property
    def limit(self) -> int:
        """Get limit for MongoDB"""
        return self.per_page

    def paginate(
        self,
        data: List[Any],
        total: int,
        message: str = "Data retrieved successfully"
    ) -> PaginatedResponse:
        """
        Create paginated response with automatic ObjectId serialization

        Args:
            data: List of data items (will auto-serialize ObjectIds)
            total: Total number of items
            message: Response message

        Returns:
            PaginatedResponse with ObjectIds automatically serialized to strings
        """
        # Automatically serialize ObjectIds in data
        serialized_data = auto_encode(data)

        # Calculate pagination metadata
        pages = ceil(total / self.per_page) if total > 0 else 1
        has_next = self.page < pages
        has_prev = self.page > 1

        meta = PaginationMeta(
            page=self.page,
            per_page=self.per_page,
            total=total,
            pages=pages,
            has_next=has_next,
            has_prev=has_prev
        )

        return PaginatedResponse(
            message=message,
            data=serialized_data,
            meta=meta
        )
